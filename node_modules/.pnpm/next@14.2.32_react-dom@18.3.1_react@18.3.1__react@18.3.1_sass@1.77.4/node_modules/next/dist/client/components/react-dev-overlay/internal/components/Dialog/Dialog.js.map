{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/Dialog.tsx"], "names": ["Dialog", "children", "type", "onClose", "props", "dialog", "setDialog", "React", "useState", "role", "setRole", "document", "hasFocus", "undefined", "onDialog", "useCallback", "node", "useOnClickOutside", "e", "preventDefault", "useEffect", "root", "getRootNode", "ShadowRoot", "shadowRoot", "handler", "el", "activeElement", "key", "HTMLElement", "getAttribute", "stopPropagation", "click", "handleFocus", "addEventListener", "window", "removeEventListener", "div", "ref", "data-nextjs-dialog", "tabIndex", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "data-nextjs-dialog-banner", "className"], "mappings": ";;;;+BA0FS<PERSON>;;;eAAAA;;;;;iEA1Fc;mCACW;AAUlC,MAAMA,SAAgC,SAASA,OAAO,KAKrD;IALqD,IAAA,EACpDC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACP,GAAGC,OACJ,GALqD;IAMpD,MAAM,CAACC,QAAQC,UAAU,GAAGC,OAAMC,QAAQ,CAAwB;IAClE,MAAM,CAACC,MAAMC,QAAQ,GAAGH,OAAMC,QAAQ,CACpC,OAAOG,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAEN,MAAMC,WAAWP,OAAMQ,WAAW,CAAC,CAACC;QAClCV,UAAUU;IACZ,GAAG,EAAE;IACLC,IAAAA,oCAAiB,EAACZ,QAAQ,CAACa;QACzBA,EAAEC,cAAc;QAChB,OAAOhB,2BAAAA;IACT;IAEA,uEAAuE;IACvE,0BAA0B;IAC1BI,OAAMa,SAAS,CAAC;QACd,IAAIf,UAAU,MAAM;YAClB;QACF;QAEA,MAAMgB,OAAOhB,OAAOiB,WAAW;QAC/B,8CAA8C;QAC9C,IAAI,CAAED,CAAAA,gBAAgBE,UAAS,GAAI;YACjC;QACF;QACA,MAAMC,aAAaH;QACnB,SAASI,QAAQP,CAAgB;YAC/B,MAAMQ,KAAKF,WAAWG,aAAa;YACnC,IACET,EAAEU,GAAG,KAAK,WACVF,cAAcG,eACdH,GAAGI,YAAY,CAAC,YAAY,QAC5B;gBACAZ,EAAEC,cAAc;gBAChBD,EAAEa,eAAe;gBAEjBL,GAAGM,KAAK;YACV;QACF;QAEA,SAASC;YACP,2GAA2G;YAC3G,6EAA6E;YAC7EvB,QAAQC,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAW,WAAWU,gBAAgB,CAAC,WAAWT;QACvCU,OAAOD,gBAAgB,CAAC,SAASD;QACjCE,OAAOD,gBAAgB,CAAC,QAAQD;QAChC,OAAO;YACLT,WAAWY,mBAAmB,CAAC,WAAWX;YAC1CU,OAAOC,mBAAmB,CAAC,SAASH;YACpCE,OAAOC,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG;QAAC5B;KAAO;IAEX,qBACE,sBAACgC;QACCC,KAAKxB;QACLyB,oBAAkB;QAClBC,UAAU,CAAC;QACX/B,MAAMA;QACNgC,mBAAiBrC,KAAK,CAAC,kBAAkB;QACzCsC,oBAAkBtC,KAAK,CAAC,mBAAmB;QAC3CuC,cAAW;;0BAEX,qBAACN;gBAAIO,2BAAyB;gBAACC,WAAW,AAAC,YAAS3C;;YACnDD;;;AAGP"}