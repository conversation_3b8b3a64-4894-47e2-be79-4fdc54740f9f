{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.tsx"], "names": ["LeftRightDialogHeader", "children", "className", "previous", "next", "close", "buttonLeft", "React", "useRef", "buttonRight", "buttonClose", "nav", "set<PERSON><PERSON>", "useState", "onNav", "useCallback", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "preventDefault", "stopPropagation", "current", "focus", "ShadowRoot", "a", "activeElement", "HTMLElement", "blur", "addEventListener", "removeEventListener", "div", "data-nextjs-dialog-left-right", "ref", "button", "type", "disabled", "undefined", "aria-disabled", "onClick", "svg", "viewBox", "fill", "xmlns", "title", "path", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "data-nextjs-errors-dialog-left-right-close-button", "aria-label", "span", "aria-hidden", "CloseIcon"], "mappings": ";;;;+BAwKSA;;;eAAAA;;;;;iEAxKc;2BACG;AAU1B,MAAMA,wBACJ,SAASA,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACN,GAN8B;IAO7B,MAAMC,aAAaC,OAAMC,MAAM,CAA2B;IAC1D,MAAMC,cAAcF,OAAMC,MAAM,CAA2B;IAC3D,MAAME,cAAcH,OAAMC,MAAM,CAA2B;IAE3D,MAAM,CAACG,KAAKC,OAAO,GAAGL,OAAMM,QAAQ,CAAqB;IACzD,MAAMC,QAAQP,OAAMQ,WAAW,CAAC,CAACC;QAC/BJ,OAAOI;IACT,GAAG,EAAE;IAELT,OAAMU,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIrB,WAAWsB,OAAO,EAAE;oBACtBtB,WAAWsB,OAAO,CAACC,KAAK;gBAC1B;gBACA1B,YAAYA;YACd,OAAO,IAAIqB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIlB,YAAYmB,OAAO,EAAE;oBACvBnB,YAAYmB,OAAO,CAACC,KAAK;gBAC3B;gBACAzB,QAAQA;YACV,OAAO,IAAIoB,EAAEC,GAAG,KAAK,UAAU;gBAC7BD,EAAEE,cAAc;gBAChBF,EAAEG,eAAe;gBACjB,IAAIT,gBAAgBY,YAAY;oBAC9B,MAAMC,IAAIb,KAAKc,aAAa;oBAC5B,IAAID,KAAKA,MAAMrB,YAAYkB,OAAO,IAAIG,aAAaE,aAAa;wBAC9DF,EAAEG,IAAI;wBACN;oBACF;gBACF;gBAEA7B,yBAAAA;YACF;QACF;QAEAa,KAAKiB,gBAAgB,CAAC,WAAWZ;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEe,gBAAgB,CAAC,WAAWZ;QAChC;QACA,OAAO;YACLL,KAAKkB,mBAAmB,CAAC,WAAWb;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAEgB,mBAAmB,CAAC,WAAWb;YACnC;QACF;IACF,GAAG;QAAClB;QAAOM;QAAKP;QAAMD;KAAS;IAE/B,2EAA2E;IAC3E,2CAA2C;IAC3CI,OAAMU,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBY,YAAY;YAC9B,MAAMC,IAAIb,KAAKc,aAAa;YAE5B,IAAI7B,YAAY,MAAM;gBACpB,IAAIG,WAAWsB,OAAO,IAAIG,MAAMzB,WAAWsB,OAAO,EAAE;oBAClDtB,WAAWsB,OAAO,CAACM,IAAI;gBACzB;YACF,OAAO,IAAI9B,QAAQ,MAAM;gBACvB,IAAIK,YAAYmB,OAAO,IAAIG,MAAMtB,YAAYmB,OAAO,EAAE;oBACpDnB,YAAYmB,OAAO,CAACM,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACvB;QAAKP;QAAMD;KAAS;IAExB,qBACE,sBAACkC;QAAIC,+BAA6B;QAACpC,WAAWA;;0BAC5C,sBAACS;gBAAI4B,KAAKzB;;kCACR,qBAAC0B;wBACCD,KAAKjC;wBACLmC,MAAK;wBACLC,UAAUvC,YAAY,OAAO,OAAOwC;wBACpCC,iBAAezC,YAAY,OAAO,OAAOwC;wBACzCE,SAAS1C,mBAAAA,WAAYwC;kCAErB,cAAA,sBAACG;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8CAEN,qBAACC;8CAAM;;8CACP,qBAACC;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;kCAIrB,qBAACf;wBACCD,KAAK9B;wBACLgC,MAAK;wBACLC,UAAUtC,QAAQ,OAAO,OAAOuC;wBAChCC,iBAAexC,QAAQ,OAAO,OAAOuC;wBACrCE,SAASzC,eAAAA,OAAQuC;kCAEjB,cAAA,sBAACG;4BACCC,SAAQ;4BACRC,MAAK;4BACLC,OAAM;;8CAEN,qBAACC;8CAAM;;8CACP,qBAACC;oCACC/B,GAAE;oCACFgC,QAAO;oCACPC,aAAY;oCACZC,eAAc;oCACdC,gBAAe;;;;;oBAIpBtD;;;YAEFI,sBACC,qBAACmC;gBACCgB,mDAAiD;gBACjDjB,KAAK7B;gBACL+B,MAAK;gBACLI,SAASxC;gBACToD,cAAW;0BAEX,cAAA,qBAACC;oBAAKC,eAAY;8BAChB,cAAA,qBAACC,oBAAS;;iBAGZ;;;AAGV"}