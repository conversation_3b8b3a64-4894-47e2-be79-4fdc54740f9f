{"version": 3, "sources": ["../../src/cli/next-dev.ts"], "names": ["nextDev", "dir", "child", "config", "isTurboSession", "traceUploadUrl", "sessionStopHandled", "sessionStarted", "Date", "now", "handleSessionStop", "signal", "pid", "kill", "exitCode", "signalCode", "once", "catch", "eventCliSessionStopped", "require", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "telemetry", "traceGlobals", "get", "Telemetry", "distDir", "path", "join", "pagesDir", "appDir", "pagesResult", "findPagesDir", "record", "cliCommand", "turboFlag", "durationMilliseconds", "flushDetached", "_", "uploadTrace", "mode", "projectDir", "process", "stdout", "write", "exit", "on", "options", "portSource", "directory", "getProjectDir", "env", "NEXT_PRIVATE_DEV_DIR", "fileExists", "FileType", "Directory", "printAndExit", "preflight", "skipOnReb<PERSON>", "getPackageVersion", "getDependencies", "Promise", "resolve", "sassVersion", "nodeSassVersion", "all", "cwd", "name", "Log", "warn", "dependencies", "devDependencies", "command", "getNpxCommand", "port", "isPortIsReserved", "getReservedPortExplanation", "allowRetry", "host", "hostname", "experimentalUploadTrace", "NEXT_TRACE_UPLOAD_DISABLED", "analyticsId", "devServerOptions", "isDev", "turbo", "TURBOPACK", "setGlobal", "startServerPath", "startServer", "startServerOptions", "resolved", "defaultEnv", "initialEnv", "NODE_OPTIONS", "getNodeOptionsWithoutInspect", "nodeDebugType", "checkNodeDebugType", "maxOldSpaceSize", "getMaxOldSpaceSize", "NEXT_DISABLE_MEM_OVERRIDE", "totalMem", "os", "totalmem", "totalMemInMB", "Math", "floor", "getDebugPort", "fork", "stdio", "NEXT_PRIVATE_WORKER", "NODE_EXTRA_CA_CERTS", "selfSignedCertificate", "rootCA", "msg", "nextWorkerReady", "send", "nextWorkerOptions", "nextServerReady", "code", "RESTART_EXIT_CODE", "sync", "runDevServer", "reboot", "experimentalHttps", "certificate", "key", "experimentalHttpsKey", "cert", "experimentalHttpsCert", "experimentalHttpsCa", "undefined", "createSelfSignedCertificate", "err", "console", "error"], "mappings": ";;;;;+BAsVSA;;;eAAAA;;;QApVF;uBASA;6DACc;+BACS;2BACW;6DACxB;wBAEuB;yBACd;+DACH;8BACM;4BACQ;+BACP;wBACc;oEAEpB;qBACG;+BACN;iCAKd;2DACQ;4BACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAerB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,iBAAiB;AACrB,IAAIC;AACJ,IAAIC,qBAAqB;AACzB,IAAIC,iBAAiBC,KAAKC,GAAG;AAE7B,MAAMC,oBAAoB,OAAOC;IAC/B,IAAIT,yBAAAA,MAAOU,GAAG,EAAEV,MAAMW,IAAI,CAACF,UAAU;IACrC,IAAIL,oBAAoB;IACxBA,qBAAqB;IAErB,IAAIJ,CAAAA,yBAAAA,MAAOU,GAAG,KAAIV,MAAMY,QAAQ,KAAK,QAAQZ,MAAMa,UAAU,KAAK,MAAM;QACtE,MAAMC,IAAAA,gBAAI,EAACd,OAAO,QAAQe,KAAK,CAAC,KAAO;IACzC;IAEA,IAAI;QACF,MAAM,EAAEC,sBAAsB,EAAE,GAC9BC,QAAQ;QAEVhB,SAASA,UAAW,MAAMiB,IAAAA,eAAU,EAACC,mCAAwB,EAAEpB;QAE/D,IAAIqB,YACF,AAACC,oBAAY,CAACC,GAAG,CAAC,gBAGlB,IAAIC,kBAAS,CAAC;YACZC,SAASC,aAAI,CAACC,IAAI,CAAC3B,KAAKE,OAAOuB,OAAO;QACxC;QAEF,IAAIG,WAAoB,CAAC,CAACN,oBAAY,CAACC,GAAG,CAAC;QAC3C,IAAIM,SAAkB,CAAC,CAACP,oBAAY,CAACC,GAAG,CAAC;QAEzC,IACE,OAAOD,oBAAY,CAACC,GAAG,CAAC,gBAAgB,eACxC,OAAOD,oBAAY,CAACC,GAAG,CAAC,cAAc,aACtC;YACA,MAAMO,cAAcC,IAAAA,0BAAY,EAAC/B;YACjC6B,SAAS,CAAC,CAACC,YAAYD,MAAM;YAC7BD,WAAW,CAAC,CAACE,YAAYF,QAAQ;QACnC;QAEAP,UAAUW,MAAM,CACdf,uBAAuB;YACrBgB,YAAY;YACZC,WAAW/B;YACXgC,sBAAsB5B,KAAKC,GAAG,KAAKF;YACnCsB;YACAC;QACF,IACA;QAEFR,UAAUe,aAAa,CAAC,OAAOpC;IACjC,EAAE,OAAOqC,GAAG;IACV,6CAA6C;IAC7C,sBAAsB;IACxB;IAEA,IAAIjC,gBAAgB;QAClBkC,IAAAA,oBAAW,EAAC;YACVlC;YACAmC,MAAM;YACNC,YAAYxC;YACZyB,SAASvB,OAAOuB,OAAO;QACzB;IACF;IAEA,yDAAyD;IACzD,iDAAiD;IACjDgB,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQC,MAAM,CAACC,KAAK,CAAC;IACrBF,QAAQG,IAAI,CAAC;AACf;AAEAH,QAAQI,EAAE,CAAC,UAAU,IAAMpC,kBAAkB;AAC7CgC,QAAQI,EAAE,CAAC,WAAW,IAAMpC,kBAAkB;AAE9C,iCAAiC;AACjCgC,QAAQI,EAAE,CAAC,QAAQ,IAAM5C,yBAAAA,MAAOW,IAAI,CAAC;AAErC,MAAMb,UAAU,OACd+C,SACAC,YACAC;IAEAhD,MAAMiD,IAAAA,4BAAa,EAACR,QAAQS,GAAG,CAACC,oBAAoB,IAAIH;IAExD,4CAA4C;IAC5C,IAAI,CAAE,MAAMI,IAAAA,sBAAU,EAACpD,KAAKqD,oBAAQ,CAACC,SAAS,GAAI;QAChDC,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEvD,IAAI,CAAC;IACvE;IAEA,eAAewD,UAAUC,YAAqB;QAC5C,MAAM,EAAEC,iBAAiB,EAAEC,eAAe,EAAE,GAAI,MAAMC,QAAQC,OAAO,CACnE3C,QAAQ;QAGV,MAAM,CAAC4C,aAAaC,gBAAgB,GAAG,MAAMH,QAAQI,GAAG,CAAC;YACvDN,kBAAkB;gBAAEO,KAAKjE;gBAAKkE,MAAM;YAAO;YAC3CR,kBAAkB;gBAAEO,KAAKjE;gBAAKkE,MAAM;YAAY;SACjD;QACD,IAAIJ,eAAeC,iBAAiB;YAClCI,KAAIC,IAAI,CACN,mHACE,iEACA;QAEN;QAEA,IAAI,CAACX,cAAc;YACjB,MAAM,EAAEY,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMX,gBAAgB;gBAC9DM,KAAKjE;YACP;YAEA,6GAA6G;YAC7G,IACEqE,YAAY,CAAC,aAAa,IACzBC,eAAe,CAAC,aAAa,IAC5BA,eAAe,CAAC,aAAa,KAAK,eACpC;gBACA,MAAMC,UAAUC,IAAAA,4BAAa,EAACxE;gBAC9BmE,KAAIC,IAAI,CACN,2GACE,6DACA,CAAC,6BAA6B,EAAEG,QAAQ,4GAA4G,CAAC;YAE3J;QACF;IACF;IAEA,MAAME,OAAO3B,QAAQ2B,IAAI;IAEzB,IAAIC,IAAAA,iCAAgB,EAACD,OAAO;QAC1BlB,IAAAA,mBAAY,EAACoB,IAAAA,2CAA0B,EAACF,OAAO;IACjD;IAEA,2EAA2E;IAC3E,MAAMG,aAAa7B,eAAe;IAElC,8DAA8D;IAC9D,0DAA0D;IAC1D,MAAM8B,OAAO/B,QAAQgC,QAAQ;IAE7B5E,SAAS,MAAMiB,IAAAA,eAAU,EAACC,mCAAwB,EAAEpB;IAEpD,IACE8C,QAAQiC,uBAAuB,IAC/B,CAACtC,QAAQS,GAAG,CAAC8B,0BAA0B,EACvC;QACA5E,iBAAiB0C,QAAQiC,uBAAuB;IAClD;IAEA,yCAAyC;IACzC,IAAI7E,OAAO+E,WAAW,EAAE;QACtBd,KAAIC,IAAI,CACN,CAAC,kJAAkJ,CAAC;IAExJ;IAEA,MAAMc,mBAAuC;QAC3ClF;QACAyE;QACAG;QACAO,OAAO;QACPL,UAAUD;IACZ;IAEA,IAAI/B,QAAQsC,KAAK,EAAE;QACjB3C,QAAQS,GAAG,CAACmC,SAAS,GAAG;IAC1B;IAEAlF,iBAAiB,CAAC,CAACsC,QAAQS,GAAG,CAACmC,SAAS;IAExC,MAAM5D,UAAUC,aAAI,CAACC,IAAI,CAAC3B,KAAKE,OAAOuB,OAAO,IAAI;IACjD6D,IAAAA,iBAAS,EAAC,SAASlE,mCAAwB;IAC3CkE,IAAAA,iBAAS,EAAC,WAAW7D;IAErB,MAAM8D,kBAAkBrE,QAAQ2C,OAAO,CAAC;IAExC,eAAe2B,YAAYC,kBAAsC;QAC/D,OAAO,IAAI7B,QAAc,CAACC;YACxB,IAAI6B,WAAW;YACf,MAAMC,aAAcC,eAAU,IAAInD,QAAQS,GAAG;YAE7C,IAAI2C,eAAeC,IAAAA,mCAA4B;YAC/C,IAAIC,gBAAgBC,IAAAA,yBAAkB;YAEtC,MAAMC,kBAAkBC,IAAAA,yBAAkB;YAE1C,IAAI,CAACD,mBAAmB,CAACxD,QAAQS,GAAG,CAACiD,yBAAyB,EAAE;gBAC9D,MAAMC,WAAWC,WAAE,CAACC,QAAQ;gBAC5B,MAAMC,eAAeC,KAAKC,KAAK,CAACL,WAAW,OAAO;gBAClDP,eAAe,CAAC,EAAEA,aAAa,sBAAsB,EAAEW,KAAKC,KAAK,CAC/DF,eAAe,KACf,CAAC;YACL;YAEA,IAAIR,eAAe;gBACjBF,eAAe,CAAC,EAAEA,aAAa,GAAG,EAAEE,cAAc,CAAC,EACjDW,IAAAA,mBAAY,MAAK,EAClB,CAAC;YACJ;YAEAzG,QAAQ0G,IAAAA,mBAAI,EAACpB,iBAAiB;gBAC5BqB,OAAO;gBACP1D,KAAK;oBACH,GAAGyC,UAAU;oBACbN,WAAW5C,QAAQS,GAAG,CAACmC,SAAS;oBAChCwB,qBAAqB;oBACrBC,qBAAqBrB,mBAAmBsB,qBAAqB,GACzDtB,mBAAmBsB,qBAAqB,CAACC,MAAM,GAC/CrB,WAAWmB,mBAAmB;oBAClCjB;gBACF;YACF;YAEA5F,MAAM4C,EAAE,CAAC,WAAW,CAACoE;gBACnB,IAAIA,OAAO,OAAOA,QAAQ,UAAU;oBAClC,IAAIA,IAAIC,eAAe,EAAE;wBACvBjH,yBAAAA,MAAOkH,IAAI,CAAC;4BAAEC,mBAAmB3B;wBAAmB;oBACtD,OAAO,IAAIwB,IAAII,eAAe,IAAI,CAAC3B,UAAU;wBAC3CA,WAAW;wBACX7B;oBACF;gBACF;YACF;YAEA5D,MAAM4C,EAAE,CAAC,QAAQ,OAAOyE,MAAM5G;gBAC5B,IAAIL,sBAAsBK,QAAQ;oBAChC;gBACF;gBACA,IAAI4G,SAASC,wBAAiB,EAAE;oBAC9B,uEAAuE;oBACvE,oEAAoE;oBACpE,wBAAwB;oBACxB,IAAInH,gBAAgB;wBAClBkC,IAAAA,oBAAW,EAAC;4BACVlC;4BACAmC,MAAM;4BACNC,YAAYxC;4BACZyB,SAASvB,OAAOuB,OAAO;4BACvB+F,MAAM;wBACR;oBACF;oBACA,OAAOhC,YAAYC;gBACrB;gBACA,MAAMhF,kBAAkBC;YAC1B;QACF;IACF;IAEA,MAAM+G,eAAe,OAAOC;QAC1B,IAAI;YACF,IAAI,CAAC,CAAC5E,QAAQ6E,iBAAiB,EAAE;gBAC/BxD,KAAIC,IAAI,CACN;gBAGF,IAAIwD;gBAEJ,MAAMC,MAAM/E,QAAQgF,oBAAoB;gBACxC,MAAMC,OAAOjF,QAAQkF,qBAAqB;gBAC1C,MAAMhB,SAASlE,QAAQmF,mBAAmB;gBAE1C,IAAIJ,OAAOE,MAAM;oBACfH,cAAc;wBACZC,KAAKnG,aAAI,CAACmC,OAAO,CAACgE;wBAClBE,MAAMrG,aAAI,CAACmC,OAAO,CAACkE;wBACnBf,QAAQA,SAAStF,aAAI,CAACmC,OAAO,CAACmD,UAAUkB;oBAC1C;gBACF,OAAO;oBACLN,cAAc,MAAMO,IAAAA,mCAA2B,EAACtD;gBAClD;gBAEA,MAAMW,YAAY;oBAChB,GAAGN,gBAAgB;oBACnB6B,uBAAuBa;gBACzB;YACF,OAAO;gBACL,MAAMpC,YAAYN;YACpB;YAEA,MAAM1B,UAAUkE;QAClB,EAAE,OAAOU,KAAK;YACZC,QAAQC,KAAK,CAACF;YACd3F,QAAQG,IAAI,CAAC;QACf;IACF;IAEA,MAAM6E,aAAa;AACrB"}