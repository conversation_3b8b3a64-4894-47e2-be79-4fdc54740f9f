{"version": 3, "sources": ["../../src/cli/next-info.ts"], "names": ["nextInfo", "getPackageVersion", "packageName", "require", "version", "getNextConfig", "config", "loadConfig", "PHASE_INFO", "process", "cwd", "output", "experimental", "useWasmBinary", "getBinaryVersion", "binaryName", "childProcess", "execFileSync", "toString", "trim", "printInfo", "installedRelease", "nextConfig", "stalenessWithTitle", "title", "versionInfo", "registry", "getRegistry", "res", "fetch", "tags", "json", "parseVersionInfo", "installed", "latest", "canary", "getStaleness", "e", "console", "warn", "yellow", "bold", "message", "cyan", "cpuCores", "os", "cpus", "length", "log", "platform", "arch", "Math", "ceil", "totalmem", "versions", "node", "staleness", "startsWith", "runSharedDependencyCheck", "tools", "skipMessage", "getSupportedArchTriples", "currentPlatform", "spawn", "triples", "availableTools", "tool", "check", "sync", "bin", "checkArgs", "status", "push", "messages", "result", "outputs", "triple", "triplePkgName", "platformArchABI", "resolved", "resolve", "proc", "args", "procPromise", "Promise", "stdout", "on", "data", "stderr", "c", "code", "join", "printVerboseInfo", "fs", "tasks", "scripts", "default", "isWsl", "ciInfo", "is<PERSON>ock<PERSON>", "isCI", "name", "report", "getReport", "header", "javascriptHeap", "sharedObjects", "commandLine", "host", "networkInterfaces", "reportSummary", "JSON", "stringify", "platformArchTriples", "loadBindings", "bindings", "target", "getTargetTriple", "path", "fallbackBindingsDirectory", "nextPath", "dirname", "tryResolve", "pkgName", "fileExists", "existsSync", "loadError", "loadSuccess", "linux", "win32", "darwin", "task", "targetPlatform", "undefined", "taskScript", "taskResult", "options", "verbose"], "mappings": ";;;;;+BA+kBSA;;;eAAAA;;;2DA7kBM;sEACU;4BAEU;2BACR;+DACJ;6BACK;kCACK;sCACJ;qBACR;;;;;;AA2CrB,SAASC,kBAAkBC,WAAmB;IAC5C,IAAI;QACF,OAAOC,QAAQ,CAAC,EAAED,YAAY,aAAa,CAAC,EAAEE,OAAO;IACvD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,eAAeC;QAMMC;IALnB,MAAMA,SAAS,MAAMC,IAAAA,eAAU,EAACC,qBAAU,EAAEC,QAAQC,GAAG;IAEvD,OAAO;QACLC,QAAQL,OAAOK,MAAM,IAAI;QACzBC,cAAc;YACZC,aAAa,GAAEP,uBAAAA,OAAOM,YAAY,qBAAnBN,qBAAqBO,aAAa;QACnD;IACF;AACF;AAEA;;;CAGC,GACD,SAASC,iBAAiBC,UAAkB;IAC1C,IAAI;QACF,OAAOC,sBAAY,CAChBC,YAAY,CAACF,YAAY;YAAC;SAAY,EACtCG,QAAQ,GACRC,IAAI;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA;;CAEC,GACD,eAAeC;IACb,MAAMC,mBAAmBpB,kBAAkB;IAC3C,MAAMqB,aAAa,MAAMjB;IAEzB,IAAIkB,qBAAqB;IACzB,IAAIC,QAAQ;IACZ,IAAIC;IAEJ,IAAI;QACF,MAAMC,WAAWC,IAAAA,wBAAW;QAC5B,MAAMC,MAAM,MAAMC,MAAM,CAAC,EAAEH,SAAS,wBAAwB,CAAC;QAC7D,MAAMI,OAAO,MAAMF,IAAIG,IAAI;QAE3BN,cAAcO,IAAAA,kCAAgB,EAAC;YAC7BC,WAAWZ;YACXa,QAAQJ,KAAKI,MAAM;YACnBC,QAAQL,KAAKK,MAAM;QACrB;QAEAX,QAAQY,IAAAA,kCAAY,EAACX,aAAaD,KAAK;QAEvC,IAAIA,OAAO;YACTD,qBAAqB,CAAC,IAAI,EAAEC,MAAM,CAAC;QACrC;IACF,EAAE,OAAOa,GAAG;QACVC,QAAQC,IAAI,CACV,CAAC,EAAEC,IAAAA,kBAAM,EACPC,IAAAA,gBAAI,EAAC,SACL,oDAAoD,EACpD,AAACJ,EAAYK,OAAO,CACrB;gBACS,EAAErB,iBAAiB;;oBAEf,EAAEsB,IAAAA,gBAAI,EAClB,qDACA,CAAC;IAEP;IAEA,MAAMC,WAAWC,WAAE,CAACC,IAAI,GAAGC,MAAM;IACjCT,QAAQU,GAAG,CAAC,CAAC;;YAEH,EAAEH,WAAE,CAACI,QAAQ,GAAG;QACpB,EAAEJ,WAAE,CAACK,IAAI,GAAG;WACT,EAAEL,WAAE,CAACzC,OAAO,GAAG;yBACD,EAAE+C,KAAKC,IAAI,CAACP,WAAE,CAACQ,QAAQ,KAAK,OAAO,MAAM;uBAC3C,EAAET,WAAW,IAAIA,WAAW,MAAM;;QAEjD,EAAEnC,QAAQ6C,QAAQ,CAACC,IAAI,CAAC;OACzB,EAAEzC,iBAAiB,OAAO;QACzB,EAAEA,iBAAiB,QAAQ;QAC3B,EAAEA,iBAAiB,QAAQ;;QAE3B,EAAEO,iBAAiB,EAAEE,mBAAmB;sBAC1B,EAAEtB,kBAAkB,sBAAsB;SACvD,EAAEA,kBAAkB,SAAS;aACzB,EAAEA,kBAAkB,aAAa;cAChC,EAAEA,kBAAkB,cAAc;;UAEtC,EAAEqB,WAAWX,MAAM,CAAC,CAAC;IAE7B,IAAIc,+BAAAA,YAAa+B,SAAS,CAACC,UAAU,CAAC,UAAU;QAC9ClB,IAAAA,SAAI,EAAC,CAAC,EAAEf,MAAM;;gEAE8C,CAAC;IAC/D;AACF;AAEA;;;;;CAKC,GACD,eAAekC,yBACbC,KAA4E,EAC5EC,WAAmB;QAKHC;IAHhB,MAAMC,kBAAkBjB,WAAE,CAACI,QAAQ;IACnC,MAAMc,QAAQ5D,QAAQ;IACtB,MAAM,EAAE0D,uBAAuB,EAAE,GAAG1D,QAAQ;IAC5C,MAAM6D,UAAUH,EAAAA,2CAAAA,yBAAyB,CAACC,gBAAgB,qBAA1CD,wCAA4C,CAAChB,WAAE,CAACK,IAAI,GAAG,KAAI,EAAE;IAC7E,mFAAmF;IAEnF,MAAMe,iBAAiB,EAAE;IACzB,KAAK,MAAMC,QAAQP,MAAO;QACxB,IAAI;YACF,MAAMQ,QAAQJ,MAAMK,IAAI,CAACF,KAAKG,GAAG,EAAEH,KAAKI,SAAS;YACjD,IAAIH,MAAMI,MAAM,KAAK,GAAG;gBACtBN,eAAeO,IAAI,CAACN;YACtB;QACF,EAAE,OAAM;QACN,kCAAkC;QACpC;IACF;IAEA,IAAID,eAAelB,MAAM,KAAK,GAAG;QAC/B,OAAO;YACL0B,UAAUb;YACVc,QAAQ;QACV;IACF;IAEA,MAAMC,UAAyB,EAAE;IACjC,IAAID,SAA0B;IAE9B,KAAK,MAAME,UAAUZ,QAAS;QAC5B,MAAMa,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;QAC3D,IAAIC;QACJ,IAAI;YACFA,WAAW5E,QAAQ6E,OAAO,CAACH;QAC7B,EAAE,OAAOxC,GAAG;YACV,OAAO;gBACLoC,UACE;gBACFC,QAAQ;YACV;QACF;QAEA,KAAK,MAAMR,QAAQD,eAAgB;YACjC,MAAMgB,OAAOlB,MAAMG,KAAKG,GAAG,EAAE;mBAAIH,KAAKgB,IAAI;gBAAEH;aAAS;YACrDJ,QAAQH,IAAI,CAAC,CAAC,QAAQ,EAAEN,KAAKG,GAAG,CAAC,eAAe,CAAC;YACjD,yFAAyF;YACzF,MAAMc,cAAc,IAAIC,QAAQ,CAACJ;gBAC/BC,KAAKI,MAAM,CAACC,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQH,IAAI,CAACe;gBACf;gBACAN,KAAKO,MAAM,CAACF,EAAE,CAAC,QAAQ,SAAUC,IAAY;oBAC3CZ,QAAQH,IAAI,CAACe;gBACf;gBACAN,KAAKK,EAAE,CAAC,SAAS,CAACG,IAAWT,QAAQS;YACvC;YAEA,IAAIC,OAAO,MAAMP;YACjB,IAAIO,SAAS,GAAG;gBACdhB,SAAS;YACX;QACF;IACF;IAEA,OAAO;QACL/D,QAAQgE,QAAQgB,IAAI,CAAC;QACrBjB;IACF;AACF;AAEA;;CAEC,GACD,eAAekB;IACb,MAAMC,KAAK1F,QAAQ;IACnB,MAAM2D,kBAAkBjB,WAAE,CAACI,QAAQ;IAEnC,IACEa,oBAAoB,WACpBA,oBAAoB,WACpBA,oBAAoB,UACpB;QACAxB,QAAQU,GAAG,CACT;QAEF;IACF;IAEA,wBAAwB;IACxB,MAAM8C,QAKD;QACH;YACEtE,OAAO;YACPuE,SAAS;gBACPC,SAAS;oBACP,+FAA+F;oBAC/F,2CAA2C;oBAC3C,MAAMC,QAAQ9F,QAAQ;oBACtB,MAAM+F,SAAS/F,QAAQ;oBACvB,MAAMgG,WAAWhG,QAAQ;oBAEzB,MAAMQ,SAAS,CAAC;OACnB,EAAEsF,MAAM;UACL,EAAEE,WAAW;MACjB,EAAED,OAAOE,IAAI,GAAGF,OAAOG,IAAI,IAAI,YAAY,QAAQ;AACzD,CAAC;oBAES,OAAO;wBACL1F;wBACA+D,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACElD,OAAO;YACPuE,SAAS;gBACPC,SAAS;oBACP,MAAM3E,mBAAmBpB,kBAAkB;oBAC3C,MAAMqB,aAAa,MAAMjB;oBACzB,MAAMM,SAAS,CAAC;;UAEhB,EAAEF,QAAQ6C,QAAQ,CAACC,IAAI,CAAC;SACzB,EAAEzC,iBAAiB,OAAO;UACzB,EAAEA,iBAAiB,QAAQ;UAC3B,EAAEA,iBAAiB,QAAQ;;UAE3B,EAAEO,iBAAiB;wBACL,EAAEpB,kBAAkB,sBAAsB;WACvD,EAAEA,kBAAkB,SAAS;eACzB,EAAEA,kBAAkB,aAAa;gBAChC,EAAEA,kBAAkB,cAAc;;YAEtC,EAAEqB,WAAWX,MAAM,CAAC;;AAEhC,CAAC;oBACS,OAAO;wBACLA;wBACA+D,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACElD,OAAO;YACPuE,SAAS;gBACPC,SAAS;wBACQvF;oBAAf,MAAM6F,UAAS7F,kBAAAA,QAAQ6F,MAAM,qBAAd7F,gBAAgB8F,SAAS;oBAExC,IAAI,CAACD,QAAQ;wBACX,OAAO;4BACL7B,UAAU;4BACVC,QAAQ;wBACV;oBACF;oBAEA,MAAM,EAAE8B,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAE,GAC7CJ;oBACF,mEAAmE;oBAC5DE,+BAAAA,OAAQ9F,GAAG;oBACX8F,+BAAAA,OAAQG,WAAW;oBACnBH,+BAAAA,OAAQI,IAAI;oBACZJ,+BAAAA,OAAQ1D,IAAI;oBACZ0D,+BAAAA,OAAQK,iBAAiB;oBAEhC,MAAMC,gBAAgB;wBACpBN;wBACAC;wBACAC;oBACF;oBAEA,OAAO;wBACL/F,QAAQoG,KAAKC,SAAS,CAACF,eAAe,MAAM;wBAC5CpC,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACElD,OAAO;YACPuE,SAAS;gBACPC,SAAS;wBAyBSiB;oBAxBhB,MAAMtG,SAAS,EAAE;oBAEjB,gDAAgD;oBAChD,IAAI;4BAIAW;wBAHF,IAAIA,aAAa,MAAMjB;wBACvB,MAAM,EAAE6G,YAAY,EAAE,GAAG/G,QAAQ;wBACjC,MAAMgH,WAAW,MAAMD,cACrB5F,2BAAAA,WAAWV,YAAY,qBAAvBU,yBAAyBT,aAAa;wBAExC,qEAAqE;wBACrE,MAAMuG,SAASD,SAASE,eAAe;wBAEvC,uEAAuE;wBACvE,OAAO;4BACL1G,QAAQ,CAAC,oCAAoC,EAAEyG,OAAO,CAAC;4BACvD1C,QAAQ;wBACV;oBACF,EAAE,OAAOrC,GAAG;wBACV1B,OAAO6D,IAAI,CAAC,CAAC,uBAAuB,EAAE,AAACnC,EAAYK,OAAO,CAAC,CAAC;oBAC9D;oBAEA,MAAM,EACJuE,mBAAmB,EACpB,GAAG9G,QAAQ;oBACZ,MAAM6D,WAAUiD,uCAAAA,mBAAmB,CAACnD,gBAAgB,qBAApCmD,oCAAsC,CAACpE,WAAE,CAACK,IAAI,GAAG;oBAEjE,IAAI,CAACc,WAAWA,QAAQjB,MAAM,KAAK,GAAG;wBACpC,OAAO;4BACL0B,UAAU,CAAC,4BAA4B,EAAEX,gBAAgB,GAAG,EAAEjB,WAAE,CAACK,IAAI,GAAG,CAAC;4BACzEwB,QAAQ;wBACV;oBACF;oBAEA,qGAAqG;oBACrG,MAAM4C,OAAOnH,QAAQ;oBACrB,IAAIoH;oBACJ,IAAI;wBACF,MAAMC,WAAWF,KAAKG,OAAO,CAACtH,QAAQ6E,OAAO,CAAC;wBAC9CuC,4BAA4BD,KAAK3B,IAAI,CAAC6B,UAAU;oBAClD,EAAE,OAAOnF,GAAG;oBACV,mGAAmG;oBACrG;oBAEA,MAAMqF,aAAa,CAACC;wBAClB,IAAI;4BACF,MAAM5C,WAAW5E,QAAQ6E,OAAO,CAAC2C;4BACjC,MAAMC,aAAa/B,GAAGgC,UAAU,CAAC9C;4BACjC,IAAI+C;4BACJ,IAAIC;4BAEJ,IAAI;gCACFA,cAAc,CAAC,CAAC5H,QAAQ4E,UAAUsC,eAAe;4BACnD,EAAE,OAAOhF,GAAG;gCACVyF,YAAY,AAACzF,EAAYK,OAAO;4BAClC;4BAEA/B,OAAO6D,IAAI,CACT,CAAC,EAAEmD,QAAQ,SAAS,EAAEC,WAAW,gBAAgB,EAAEG,YAAY,CAAC;4BAElE,IAAID,WAAW;gCACbnH,OAAO6D,IAAI,CAAC,CAAC,EAAEmD,QAAQ,cAAc,EAAEG,aAAa,UAAU,CAAC;4BACjE;4BAEA,IAAIC,aAAa;gCACf,OAAO;4BACT;wBACF,EAAE,OAAO1F,GAAG;4BACV1B,OAAO6D,IAAI,CACT,CAAC,EAAEmD,QAAQ,iBAAiB,EAC1B,AAACtF,EAAYK,OAAO,IAAI,UACzB,CAAC;wBAEN;wBACA,OAAO;oBACT;oBAEA,KAAK,MAAMkC,UAAUZ,QAAS;wBAC5B,MAAMa,gBAAgB,CAAC,UAAU,EAAED,OAAOE,eAAe,CAAC,CAAC;wBAC3D,yFAAyF;wBACzF,mHAAmH;wBACnH,IAAI4C,WAAW7C,gBAAgB;4BAC7B;wBACF;wBAEA,4CAA4C;wBAC5C,IAAI,CAAC0C,2BAA2B;4BAC9B;wBACF;wBAEAG,WAAWJ,KAAK3B,IAAI,CAAC4B,2BAA2B1C;oBAClD;oBAEA,OAAO;wBACLlE,QAAQA,OAAOgF,IAAI,CAAC;wBACpBjB,QAAQ;oBACV;gBACF;YACF;QACF;QACA;YACE,oFAAoF;YACpF,+EAA+E;YAC/E,mEAAmE;YACnElD,OAAO;YACPuE,SAAS;gBACPiC,OAAO;oBACL,MAAMpE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAS;4BACrBY,MAAM;gCAAC;6BAAY;wBACrB;qBACD,EACDtB;gBAEJ;gBACAqE,OAAO;oBACL,MAAMrE,cAAc,CAAC;;;;;UAKrB,CAAC;oBAED,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAW;4BACvBY,MAAM;gCAAC;6BAAW;wBACpB;qBACD,EACDtB;gBAEJ;gBACAsE,QAAQ;oBACN,MAAMtE,cACJ;oBAEF,OAAO,MAAMF,yBACX;wBACE;4BACEW,KAAK;4BACLC,WAAW;gCAAC;6BAAY;4BACxBY,MAAM;gCAAC;6BAAK;wBACd;wBACA;4BACEb,KAAK;4BACLC,WAAW,EAAE;4BACbY,MAAM,EAAE;wBACV;qBACD,EACDtB;gBAEJ;YACF;QACF;KACD;IAED,4CAA4C;IAC5C,MAAM0C,SAGD,EAAE;IAEPhE,QAAQU,GAAG,CAAC;IACZ,KAAK,MAAMmF,QAAQrC,MAAO;QACxB,IAAIqC,KAAKC,cAAc,IAAID,KAAKC,cAAc,KAAKtE,iBAAiB;YAClEwC,OAAO9B,IAAI,CAAC;gBACVhD,OAAO2G,KAAK3G,KAAK;gBACjBkD,QAAQ;oBACND,UAAU4D;oBACV1H,QAAQ,CAAC,UAAU,EAAEkC,WAAE,CAACI,QAAQ,GAAG,GAAG,EAAEkF,KAAKC,cAAc,CAAC,GAAG,EAC7DD,KAAK3G,KAAK,CACX,CAAC;oBACFkD,QAAQ;gBACV;YACF;YACA;QACF;QAEA,MAAM4D,aAAaH,KAAKpC,OAAO,CAACjC,gBAAgB,IAAIqE,KAAKpC,OAAO,CAACC,OAAO;QACxE,IAAIuC;QACJ,IAAI;YACFA,aAAa,MAAMD;QACrB,EAAE,OAAOjG,GAAG;YACVkG,aAAa;gBACX9D,UAAU,CAAC,8CAA8C,EACvD,AAACpC,EAAYK,OAAO,CACrB,CAAC;gBACFgC,QAAQ;YACV;QACF;QAEApC,QAAQU,GAAG,CAAC,CAAC,EAAE,EAAEmF,KAAK3G,KAAK,CAAC,EAAE,EAAE+G,WAAW7D,MAAM,CAAC,CAAC;QACnD,IAAI6D,WAAW9D,QAAQ,EAAE;YACvBnC,QAAQU,GAAG,CAAC,CAAC,EAAE,EAAEuF,WAAW9D,QAAQ,CAAC,CAAC;QACxC;QAEA6B,OAAO9B,IAAI,CAAC;YACVhD,OAAO2G,KAAK3G,KAAK;YACjBkD,QAAQ6D;QACV;IACF;IAEAjG,QAAQU,GAAG,CAAC,CAAC,EAAE,EAAEP,IAAAA,gBAAI,EAAC,gCAAgC,CAAC;IAEvDH,QAAQU,GAAG,CAAC,CAAC,wDAAwD,CAAC;IACtE,KAAK,MAAM,EAAExB,KAAK,EAAEkD,MAAM,EAAE,IAAI4B,OAAQ;QACtChE,QAAQU,GAAG,CAAC,CAAC,MAAM,EAAExB,MAAM,CAAC;QAE5B,IAAIkD,OAAOD,QAAQ,EAAE;YACnBnC,QAAQU,GAAG,CAAC0B,OAAOD,QAAQ;QAC7B;QAEA,IAAIC,OAAO/D,MAAM,EAAE;YACjB2B,QAAQU,GAAG,CAAC0B,OAAO/D,MAAM;QAC3B;IACF;AACF;AAEA;;;;CAIC,GACD,MAAMX,WAAW,OAAOwI;IACtB,IAAIA,QAAQC,OAAO,EAAE;QACnB,MAAM7C;IACR,OAAO;QACL,MAAMxE;IACR;AACF"}