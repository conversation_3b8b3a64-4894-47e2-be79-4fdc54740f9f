{"version": 3, "sources": ["../../src/cli/next-lint.ts"], "names": ["nextLint", "eslintOptions", "options", "defaultCacheLocation", "overrideConfigFile", "config", "extensions", "ext", "resolvePluginsRelativeTo", "rulePaths", "rulesdir", "fix", "fixTypes", "fixType", "ignore<PERSON><PERSON>", "ignore", "allowInlineConfig", "inlineConfig", "reportUnusedDisableDirectives", "reportUnusedDisableDirectivesSeverity", "cache", "cacheLocation", "cacheStrategy", "errorOnUnmatchedPattern", "directory", "nextConfig", "baseDir", "getProjectDir", "existsSync", "printAndExit", "loadConfig", "PHASE_PRODUCTION_BUILD", "files", "file", "dirs", "dir", "eslint", "filesToLint", "pathsToLint", "length", "ESLINT_DEFAULT_DIRS", "reduce", "res", "d", "currDir", "join", "push", "reportErrorsOnly", "Boolean", "quiet", "maxWarnings", "formatter", "format", "strict", "outputFile", "distDir", "pagesDir", "appDir", "findPagesDir", "verifyTypeScriptSetup", "intentDirs", "filter", "typeCheckPreflight", "tsconfigPath", "typescript", "disableStaticImages", "images", "hasAppDir", "hasPagesDir", "runLintCheck", "lintDuringBuild", "then", "lintResults", "lintOutput", "output", "eventInfo", "telemetry", "Telemetry", "record", "eventLintCheckCompleted", "buildLint", "flush", "isError", "CompileError", "green", "process", "exit", "catch", "err", "message"], "mappings": ";;;;;+BAg<PERSON><PERSON>;;;eAAAA;;;oBA9JkB;sBACN;+DAEE;uBACM;yBACH;4BACJ;2BACc;8BACP;8BACA;4BACU;wBACC;+BACV;8BACD;uCACS;;;;;;AA0BtC,MAAMC,gBAAgB,CACpBC,SACAC,uBACI,CAAA;QACJC,oBAAoBF,QAAQG,MAAM,IAAI;QACtCC,YAAYJ,QAAQK,GAAG,IAAI,EAAE;QAC7BC,0BAA0BN,QAAQM,wBAAwB,IAAI;QAC9DC,WAAWP,QAAQQ,QAAQ,IAAI,EAAE;QACjCC,KAAKT,QAAQS,GAAG,IAAI;QACpBC,UAAUV,QAAQW,OAAO,IAAI;QAC7BC,YAAYZ,QAAQY,UAAU,IAAI;QAClCC,QAAQb,QAAQa,MAAM;QACtBC,mBAAmBd,QAAQe,YAAY;QACvCC,+BACEhB,QAAQiB,qCAAqC,IAAI;QACnDC,OAAOlB,QAAQkB,KAAK;QACpBC,eAAenB,QAAQmB,aAAa,IAAIlB;QACxCmB,eAAepB,QAAQoB,aAAa;QACpCC,yBAAyBrB,QAAQqB,uBAAuB,IAAI;IAC9D,CAAA;AAEA,MAAMvB,WAAW,OAAOE,SAA0BsB;QAWpBC;IAV5B,MAAMC,UAAUC,IAAAA,4BAAa,EAACH;IAE9B,yCAAyC;IACzC,IAAI,CAACI,IAAAA,cAAU,EAACF,UAAU;QACxBG,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEH,QAAQ,CAAC;IAC3E;IAEA,MAAMD,aAAa,MAAMK,IAAAA,eAAU,EAACC,kCAAsB,EAAEL;IAE5D,MAAMM,QAAQ9B,QAAQ+B,IAAI,IAAI,EAAE;IAChC,MAAMC,OAAOhC,QAAQiC,GAAG,MAAIV,qBAAAA,WAAWW,MAAM,qBAAjBX,mBAAmBS,IAAI;IACnD,MAAMG,cAAc;WAAKH,QAAQ,EAAE;WAAMF;KAAM;IAE/C,MAAMM,cAAc,AAClBD,CAAAA,YAAYE,MAAM,GAAGF,cAAcG,8BAAmB,AAAD,EACrDC,MAAM,CAAC,CAACC,KAAeC;QACvB,MAAMC,UAAUC,IAAAA,UAAI,EAACnB,SAASiB;QAE9B,IAAI,CAACf,IAAAA,cAAU,EAACgB,UAAU;YACxB,OAAOF;QACT;QAEAA,IAAII,IAAI,CAACF;QACT,OAAOF;IACT,GAAG,EAAE;IAEL,MAAMK,mBAAmBC,QAAQ9C,QAAQ+C,KAAK;IAC9C,MAAMC,cAAchD,QAAQgD,WAAW;IACvC,MAAMC,YAAYjD,QAAQkD,MAAM,IAAI;IACpC,MAAMC,SAASL,QAAQ9C,QAAQmD,MAAM;IACrC,MAAMC,aAAapD,QAAQoD,UAAU,IAAI;IAEzC,MAAMC,UAAUV,IAAAA,UAAI,EAACnB,SAASD,WAAW8B,OAAO;IAChD,MAAMpD,uBAAuB0C,IAAAA,UAAI,EAACU,SAAS,SAAS;IACpD,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAChC;IAE1C,MAAMiC,IAAAA,4CAAqB,EAAC;QAC1BxB,KAAKT;QACL6B,SAAS9B,WAAW8B,OAAO;QAC3BK,YAAY;YAACJ;YAAUC;SAAO,CAACI,MAAM,CAACb;QACtCc,oBAAoB;QACpBC,cAActC,WAAWuC,UAAU,CAACD,YAAY;QAChDE,qBAAqBxC,WAAWyC,MAAM,CAACD,mBAAmB;QAC1DE,WAAW,CAAC,CAACV;QACbW,aAAa,CAAC,CAACZ;IACjB;IAEAa,IAAAA,0BAAY,EAAC3C,SAASY,aAAa;QACjCgC,iBAAiB;QACjBrE,eAAeA,cAAcC,SAASC;QACtC4C;QACAG;QACAC;QACAG;QACAD;IACF,GACGkB,IAAI,CAAC,OAAOC;QACX,MAAMC,aACJ,OAAOD,gBAAgB,WAAWA,cAAcA,+BAAAA,YAAaE,MAAM;QAErE,IAAI,OAAOF,gBAAgB,aAAYA,+BAAAA,YAAaG,SAAS,GAAE;YAC7D,MAAMC,YAAY,IAAIC,kBAAS,CAAC;gBAC9BtB;YACF;YACAqB,UAAUE,MAAM,CACdC,IAAAA,+BAAuB,EAAC;gBACtB,GAAGP,YAAYG,SAAS;gBACxBK,WAAW;YACb;YAEF,MAAMJ,UAAUK,KAAK;QACvB;QAEA,IACE,OAAOT,gBAAgB,aACvBA,+BAAAA,YAAaU,OAAO,KACpBT,YACA;YACA,MAAM,IAAIU,0BAAY,CAACV;QACzB;QAEA,IAAIA,YAAY;YACd5C,IAAAA,mBAAY,EAAC4C,YAAY;QAC3B,OAAO,IAAID,eAAe,CAACC,YAAY;YACrC5C,IAAAA,mBAAY,EAACuD,IAAAA,iBAAK,EAAC,mCAAmC;QACxD,OAAO;YACL,0DAA0D;YAC1D,+CAA+C;YAC/CC,QAAQC,IAAI,CAAC;QACf;IACF,GACCC,KAAK,CAAC,CAACC;QACN3D,IAAAA,mBAAY,EAAC2D,IAAIC,OAAO;IAC1B;AACJ"}