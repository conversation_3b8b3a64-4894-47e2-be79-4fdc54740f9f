{"version": 3, "sources": ["../../../../src/build/babel/plugins/jsx-pragma.ts"], "names": ["types", "t", "inherits", "jsx", "visitor", "JSXElement", "_path", "state", "set", "JSXFragment", "Program", "exit", "path", "get", "pragma", "identifier", "opts", "importAs", "existingBinding", "reuseImport", "scope", "getBinding", "property", "generateUidIdentifier", "mapping", "variableDeclaration", "variableDeclarator", "memberExpression", "newPath", "isVariableDeclarator", "node", "isCallExpression", "init", "isIdentifier", "callee", "name", "parentPath", "insertAfter", "unshiftContainer", "declar", "registerBinding", "kind", "importSpecifier", "importDeclaration", "import", "importNamespace", "importNamespaceSpecifier", "importDefaultSpecifier", "stringLiteral", "module", "specifier"], "mappings": ";;;;+BAOA;;;eAAA;;;wEAFgB;;;;;;AAED,SAAf,SAAyB,EACvBA,OAAOC,CAAC,EAGT;IACC,OAAO;QACLC,UAAUC,wBAAG;QACbC,SAAS;YACPC,YAAWC,KAAK,EAAEC,KAAK;gBACrBA,MAAMC,GAAG,CAAC,OAAO;YACnB;YAEA,qEAAqE;YACrE,sCAAsC;YACtCC,aAAYH,KAAK,EAAEC,KAAK;gBACtBA,MAAMC,GAAG,CAAC,OAAO;YACnB;YAEAE,SAAS;gBACPC,MAAKC,IAAkC,EAAEL,KAAK;oBAC5C,IAAIA,MAAMM,GAAG,CAAC,QAAQ;wBACpB,MAAMC,SAASb,EAAEc,UAAU,CAACR,MAAMS,IAAI,CAACF,MAAM;wBAC7C,IAAIG,WAAWH;wBAEf,4EAA4E;wBAC5E,MAAMI,kBACJX,MAAMS,IAAI,CAACG,WAAW,KAAK,SAC3BZ,MAAMS,IAAI,CAACC,QAAQ,IACnBL,KAAKQ,KAAK,CAACC,UAAU,CAACd,MAAMS,IAAI,CAACC,QAAQ;wBAE3C,oCAAoC;wBACpC,IAAIV,MAAMS,IAAI,CAACM,QAAQ,EAAE;4BACvB,IAAIf,MAAMS,IAAI,CAACC,QAAQ,EAAE;gCACvBA,WAAWhB,EAAEc,UAAU,CAACR,MAAMS,IAAI,CAACC,QAAQ;4BAC7C,OAAO;gCACLA,WAAWL,KAAKQ,KAAK,CAACG,qBAAqB,CAAC;4BAC9C;4BAEA,MAAMC,UAAUvB,EAAEwB,mBAAmB,CAAC,OAAO;gCAC3CxB,EAAEyB,kBAAkB,CAClBZ,QACAb,EAAE0B,gBAAgB,CAChBV,UACAhB,EAAEc,UAAU,CAACR,MAAMS,IAAI,CAACM,QAAQ;6BAGrC;4BAED,qDAAqD;4BACrD,2CAA2C;4BAC3C,IAAIM;4BAEJ,IACEV,mBACAjB,EAAE4B,oBAAoB,CAACX,gBAAgBN,IAAI,CAACkB,IAAI,KAChD7B,EAAE8B,gBAAgB,CAACb,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,KACjD/B,EAAEgC,YAAY,CAACf,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,CAACE,MAAM,KACpDhB,gBAAgBN,IAAI,CAACkB,IAAI,CAACE,IAAI,CAACE,MAAM,CAACC,IAAI,KAAK,WAC/C;gCACC,CAACP,QAAQ,GACRV,gBAAgBN,IAAI,CAACwB,UAAU,CAACC,WAAW,CAACb;4BAChD,OAAO;gCACJ,CAACI,QAAQ,GAAGhB,KAAK0B,gBAAgB,CAAC,QAAQd;4BAC7C;4BAEA,KAAK,MAAMe,UAAUX,QAAQf,GAAG,CAAC,gBAAiB;gCAChDD,KAAKQ,KAAK,CAACoB,eAAe,CACxBZ,QAAQE,IAAI,CAACW,IAAI,EACjBF;4BAEJ;wBACF;wBAEA,IAAI,CAACrB,iBAAiB;4BACpB,MAAMwB,kBAAkBzC,EAAE0C,iBAAiB,CACzC;gCACEpC,MAAMS,IAAI,CAAC4B,MAAM,GAEb3C,EAAEyC,eAAe,CACfzB,UACAhB,EAAEc,UAAU,CAACR,MAAMS,IAAI,CAAC4B,MAAM,KAEhCrC,MAAMS,IAAI,CAAC6B,eAAe,GAC1B5C,EAAE6C,wBAAwB,CAAC7B,YAE3BhB,EAAE8C,sBAAsB,CAAC9B;6BAC9B,EACDhB,EAAE+C,aAAa,CAACzC,MAAMS,IAAI,CAACiC,MAAM,IAAI;4BAGvC,MAAM,CAACrB,QAAQ,GAAGhB,KAAK0B,gBAAgB,CAAC,QAAQI;4BAChD,KAAK,MAAMQ,aAAatB,QAAQf,GAAG,CAAC,cAAe;gCACjDD,KAAKQ,KAAK,CAACoB,eAAe,CACxB,UACAU;4BAEJ;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF"}