{"version": 3, "sources": ["../../../../src/build/babel/loader/index.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentTrace", "inputSource", "inputSourceMap", "filename", "resourcePath", "target", "loaderOptions", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "getOptions", "loaderSpanInner", "code", "transformedSource", "map", "outputSourceMap", "transform", "call", "nextBabelLoaderOuter", "callback", "async", "loaderSpan", "currentTraceSpan", "traceAsyncFn", "then", "err"], "mappings": ";;;;+BAuDA;;;eAAA;;;kEAtDsB;;;;;;AAGtB,eAAeA,gBAEbC,WAAiB,EACjBC,WAAmB,EACnBC,cAAyC;IAEzC,MAAMC,WAAW,IAAI,CAACC,YAAY;IAClC,MAAMC,SAAS,IAAI,CAACA,MAAM;IAC1B,MAAMC,gBAAgBN,YACnBO,UAAU,CAAC,cACZ,+DAA+D;KAC9DC,OAAO,CAAC,IAAM,IAAI,CAACC,UAAU;IAEhC,MAAMC,kBAAkBV,YAAYO,UAAU,CAAC;IAC/C,MAAM,EAAEI,MAAMC,iBAAiB,EAAEC,KAAKC,eAAe,EAAE,GACrDJ,gBAAgBF,OAAO,CAAC,IACtBO,kBAAS,CAACC,IAAI,CACZ,IAAI,EACJf,aACAC,gBACAI,eACAH,UACAE,QACAK;IAIN,OAAO;QAACE;QAAmBE;KAAgB;AAC7C;AAEA,MAAMG,uBAAuB,SAASA,qBAEpChB,WAAmB,EACnBC,cAAyC;IAEzC,MAAMgB,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACd,UAAU,CAAC;IACpDa,WACGE,YAAY,CAAC,IACZvB,gBAAgBiB,IAAI,CAAC,IAAI,EAAEI,YAAYnB,aAAaC,iBAErDqB,IAAI,CACH,CAAC,CAACX,mBAAmBE,gBAAqB,GACxCI,4BAAAA,SAAW,MAAMN,mBAAmBE,mBAAmBZ,iBACzD,CAACsB;QACCN,4BAAAA,SAAWM;IACb;AAEN;MAEA,WAAeP"}