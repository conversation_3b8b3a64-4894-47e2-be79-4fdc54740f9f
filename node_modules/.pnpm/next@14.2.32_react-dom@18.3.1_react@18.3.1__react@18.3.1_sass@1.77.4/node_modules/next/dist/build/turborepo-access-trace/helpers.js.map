{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/helpers.ts"], "names": ["turborepoTraceAccess", "writeTurborepoAccessTraceResult", "f", "parent", "process", "env", "TURBOREPO_TRACE_FILE", "withTurborepoTraceAccess", "then", "result", "proxy", "merge", "distDir", "traces", "configTraceFile", "length", "accessTrace", "otherTraces", "trace", "fs", "mkdir", "path", "dirname", "recursive", "writeFile", "JSON", "stringify", "outputs", "accessed", "toPublicTrace", "err", "Error", "cause", "envVars", "Set", "addresses", "fsPaths", "restoreTCP", "tcpProxy", "restoreEnv", "envProxy", "functionResult", "traceResult", "TurborepoAccessTraceResult"], "mappings": ";;;;;;;;;;;;;;;IAegBA,oBAAoB;eAApBA;;IAyBMC,+BAA+B;eAA/BA;;;iEAxCP;6DACE;qBAEQ;qBACA;wBACkB;;;;;;AAUpC,SAASD,qBACdE,CAAuB,EACvBC,MAAkC;IAElC,sEAAsE;IACtE,YAAY;IACZ,IAAI,CAACC,QAAQC,GAAG,CAACC,oBAAoB,EAAE,OAAOJ;IAE9C,6EAA6E;IAC7E,4EAA4E;IAC5E,uBAAuB;IACvB,OAAOK,yBAAyBL,GAAGM,IAAI,CAAC,CAAC,CAACC,QAAQC,MAAM;QACtDP,OAAOQ,KAAK,CAACD;QAEb,qCAAqC;QACrC,OAAOD;IACT;AACF;AAQO,eAAeR,gCAAgC,EACpDW,OAAO,EACPC,MAAM,EAIP;IACC,MAAMC,kBAAkBV,QAAQC,GAAG,CAACC,oBAAoB;IAExD,IAAI,CAACQ,mBAAmBD,OAAOE,MAAM,KAAK,GAAG;IAE7C,eAAe;IACf,MAAM,CAACC,aAAa,GAAGC,YAAY,GAAGJ;IACtC,KAAK,MAAMK,SAASD,YAAa;QAC/BD,YAAYL,KAAK,CAACO;IACpB;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAMC,iBAAE,CAACC,KAAK,CAACC,aAAI,CAACC,OAAO,CAACR,kBAAkB;YAAES,WAAW;QAAK;QAChE,MAAMJ,iBAAE,CAACK,SAAS,CAChBV,iBACAW,KAAKC,SAAS,CAAC;YACbC,SAAS;gBAAC,CAAC,EAAEf,QAAQ,GAAG,CAAC;gBAAE,CAAC,CAAC,EAAEA,QAAQ,SAAS,CAAC;aAAC;YAClDgB,UAAUZ,YAAYa,aAAa;QACrC;IAEJ,EAAE,OAAOC,KAAK;QACZ,gEAAgE;QAChE,qDAAqD;QACrD,MAAM,IAAIC,MAAM,CAAC,2CAA2C,CAAC,EAAE;YAC7DC,OAAOF;QACT;IACF;AACF;AAEA,eAAevB,yBACbL,CAAuB;IAEvB,MAAM+B,UAAmB,IAAIC,IAAI,EAAE;IACnC,wDAAwD;IACxD,MAAMC,YAAuB,EAAE;IAC/B,iEAAiE;IACjE,MAAMC,UAAc,IAAIF;IAExB,gBAAgB;IAChB,MAAMG,aAAaC,IAAAA,aAAQ,EAACH;IAC5B,MAAMI,aAAaC,IAAAA,aAAQ,EAACP;IAE5B,IAAIQ;IAEJ,yFAAyF;IACzF,IAAI;QACF,4BAA4B;QAC5BA,iBAAiB,MAAMvC;IACzB,SAAU;QACR,iBAAiB;QACjBmC;QACAE;IACF;IAEA,MAAMG,cAAc,IAAIC,kCAA0B,CAChDV,SACAE,WACAC;IAGF,OAAO;QAACK;QAAgBC;KAAY;AACtC"}