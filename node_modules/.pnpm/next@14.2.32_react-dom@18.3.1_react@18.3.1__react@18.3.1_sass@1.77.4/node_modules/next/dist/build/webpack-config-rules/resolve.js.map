{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "names": ["edgeConditionNames", "getMainField", "mainFieldsPerCompiler", "COMPILER_NAMES", "server", "client", "edgeServer", "compilerType", "preferEsm"], "mappings": ";;;;;;;;;;;;;;;IAMaA,kBAAkB;eAAlBA;;IAmBGC,YAAY;eAAZA;;;2BAtBT;AAGA,MAAMD,qBAAqB;IAChC;IACA;IACA,kCAAkC;IAClC;CACD;AAED,MAAME,wBAGF;IACF,2EAA2E;IAC3E,CAACC,yBAAc,CAACC,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACD,yBAAc,CAACE,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,CAACF,yBAAc,CAACG,UAAU,CAAC,EAAEN;IAC7B,iDAAiD;IACjD,cAAc;QAAC;QAAU;KAAO;AAClC;AAEO,SAASC,aACdM,YAAgC,EAChCC,SAAkB;IAElB,IAAID,iBAAiBJ,yBAAc,CAACG,UAAU,EAAE;QAC9C,OAAON;IACT,OAAO,IAAIO,iBAAiBJ,yBAAc,CAACE,MAAM,EAAE;QACjD,OAAOH,qBAAqB,CAACC,yBAAc,CAACE,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOG,YACHN,qBAAqB,CAAC,aAAa,GACnCA,qBAAqB,CAACC,yBAAc,CAACC,MAAM,CAAC;AAClD"}