{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/client.ts"], "names": ["getClientStyleLoader", "hasAppDir", "isAppDir", "isDevelopment", "assetPrefix", "shouldEnableApp", "loader", "options", "insert", "element", "anchorElement", "document", "querySelector", "parentNode", "insertBefore", "MiniCssExtractPlugin", "require", "default", "publicPath", "esModule"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,qBAAqB,EACnCC,SAAS,EACTC,QAAQ,EACRC,aAAa,EACbC,WAAW,EAMZ;IACC,MAAMC,kBAAkB,OAAOH,aAAa,YAAYA,WAAWD;IAEnE,0DAA0D;IAC1D,IAAIE,iBAAiB,CAACE,iBAAiB;QACrC,OAAO;YACLC,QAAQ;YACRC,SAAS;gBACPC,QAAQ,SAAUC,OAAa;oBAC7B,uDAAuD;oBACvD,uDAAuD;oBACvD,uDAAuD;oBACvD,sDAAsD;oBACtD,sDAAsD;oBAEtD,sDAAsD;oBACtD,yBAAyB;oBACzB,IAAIC,gBAAgBC,SAASC,aAAa,CACxC;oBAEF,IAAIC,aAAaH,cAAcG,UAAU,AAAE,kBAAkB;;oBAE7D,mDAAmD;oBACnD,uDAAuD;oBACvD,2CAA2C;oBAC3CA,WAAWC,YAAY,CAACL,SAASC;gBACnC;YACF;QACF;IACF;IAEA,MAAMK,uBACJC,QAAQ,+CAA+CC,OAAO;IAChE,OAAO;QACLX,QAAQS,qBAAqBT,MAAM;QACnCC,SAAS;YACPW,YAAY,CAAC,EAAEd,YAAY,OAAO,CAAC;YACnCe,UAAU;QACZ;IACF;AACF"}