{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "names": ["FlightClientEntryPlugin", "PLUGIN_NAME", "pluginState", "getProxiedPluginState", "serverActions", "edgeServerActions", "actionModServerId", "actionModEdgeServerId", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "path", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "resource", "layer", "WEBPACK_LAYERS", "serverSideRendering", "ssrNamedModuleId", "relative", "context", "normalizePathSep", "replace", "traverseModules", "_chunk", "_chunkGroup", "isWebpackServerOnlyLayer", "moduleGraph", "isAsync", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActions", "forEachEntryModule", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "getModuleReferencesInOrder", "entryRequest", "dependency", "request", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "names", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "APP_CLIENT_INTERNALS", "size", "actionNames", "actionName", "injectActionEntry", "actions", "invalidator", "getInvalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "COMPILER_NAMES", "client", "Promise", "all", "map", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "id", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleResource", "getActionsFromBuildInfo", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visited", "CSSImports", "filterClientComponents", "importedIdentifiers", "isCSS", "isCSSMod", "addClientImport", "webpackRuntime", "EDGE_RUNTIME_WEBPACK", "DEFAULT_RUNTIME_WEBPACK", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "isClientComponentEntryModule", "dependencyIds", "ids", "Array", "from", "loaderOptions", "modules", "regexCSS", "test", "localeCompare", "clientImportPath", "server", "clientBrowserLoader", "stringify", "sep", "x", "JSON", "clientSSRLoader", "getEntries", "page<PERSON><PERSON>", "getEntry<PERSON>ey", "PAGE_TYPES", "APP", "type", "EntryTypes", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentEntryDep", "EntryPlugin", "createDependency", "addEntry", "actionsArray", "actionLoader", "__client_imported__", "currentCompilerServerActions", "p", "generateActionId", "workers", "<PERSON><PERSON><PERSON><PERSON>", "reactServerComponents", "actionEntryDep", "resolve", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "action", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "SERVER_REFERENCE_MANIFEST", "sources", "RawSource", "modRequest", "isFirstVisitModule", "getModuleBuildInfo", "clientEntryType", "rsc", "isCjsModule", "assumedSourceType", "getAssumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier"], "mappings": ";;;;+BAuKaA;;;eAAAA;;;yBAjKW;6BACE;6DACT;sCAOV;2BACwB;4BASxB;uBAOA;wBAMA;kCAC0B;8BACK;2BACX;wBACc;oCACN;kCACE;;;;;;AASrC,MAAMC,cAAc;AAqBpB,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxC,gDAAgD;IAChDC,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,mBAAmB,CAAC;IAOpBC,uBAAuB,CAAC;IAQxB,gEAAgE;IAChEC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IAEtB,6DAA6D;IAC7D,wEAAwE;IACxE,qFAAqF;IACrF,uCAAuC;IACvCC,sBAAsB,CAAC;IAEvBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQC,aAAI,CAACC,KAAK,CAACR,OAAOS,IAAI;QACpC,MAAMC,QAAQH,aAAI,CAACC,KAAK,CAACP,OAAOQ,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACN;QAC9C,MAAMO,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIxB,iBAAkB;QACtD,KAAK,MAAMyB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWd,aAAI,CAACC,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEO,MAAMlC;IAOX6C,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;IAC5C;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BvD,aACA,CAACsD,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFZ,SAASC,KAAK,CAACY,UAAU,CAACC,UAAU,CAAClE,aAAa,CAACsD,cACjD,IAAI,CAACa,mBAAmB,CAACf,UAAUE;QAGrCF,SAASC,KAAK,CAACe,YAAY,CAACb,GAAG,CAACvD,aAAa,CAACsD;YAC5C,MAAMe,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI;gBAClE,MAAMiD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACC,sCAA0B,IAC3CC,IAAAA,qCAA6B,EAACT,IAAIU,QAAQ,EAAET,WAC5CA,UAAUG,WACZJ,IAAIU,QAAQ;gBAEhB,IAAIV,IAAIW,KAAK,KAAKC,yBAAc,CAACC,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOd,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIQ,mBAAmB3D,aAAI,CAAC4D,QAAQ,CAAClC,SAASmC,OAAO,EAAEV;oBAEvD,IAAI,CAACQ,iBAAiBP,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BO,mBAAmB,CAAC,EAAE,EAAEG,IAAAA,kCAAgB,EAACH,kBAAkB,CAAC;oBAC9D;oBAEA,IAAI,IAAI,CAACrC,YAAY,EAAE;wBACrB/C,YAAYO,mBAAmB,CAC7B6E,iBAAiBI,OAAO,CAAC,uBAAuB,eACjD,GAAGnB;oBACN,OAAO;wBACLrE,YAAYM,eAAe,CAAC8E,iBAAiB,GAAGf;oBAClD;gBACF;YACF;YAEAoB,IAAAA,uBAAe,EAACpC,aAAa,CAACiB,KAAKoB,QAAQC,aAAatB;gBACtD,IAAIC,OAAOA,IAAIU,QAAQ,IAAI,CAACY,IAAAA,gCAAwB,EAACtB,IAAIW,KAAK,GAAG;oBAC/D,IAAI5B,YAAYwC,WAAW,CAACC,OAAO,CAACxB,MAAM;wBACxC,0FAA0F;wBAC1F,4FAA4F;wBAC5FtE,YAAYQ,oBAAoB,CAAC8D,IAAIU,QAAQ,CAAC,GAAG;oBACnD;gBACF;gBAEA,IAAIX,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAnB,SAASC,KAAK,CAAC2C,IAAI,CAACzC,GAAG,CAACvD,aAAa,CAACsD;YACpCA,YAAYD,KAAK,CAAC4C,aAAa,CAAC/B,UAAU,CACxC;gBACEtC,MAAM5B;gBACNkG,OAAOvC,gBAAO,CAACwC,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAAChD,aAAa+C;QAErD;IACF;IAEA,MAAMlC,oBACJf,QAA0B,EAC1BE,WAAgC,EAChC;QACA,MAAMiD,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAA4D,CAAC;QACnE,MAAMC,iBAAiB,IAAIxE;QAE3B,4EAA4E;QAC5E,0BAA0B;QAC1ByE,IAAAA,0BAAkB,EAACtD,aAAa,CAAC,EAAE1B,IAAI,EAAEiF,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAMrG,mBAA+B,CAAC;YAEtC,KAAK,MAAMsG,cAAcC,IAAAA,kCAA0B,EACjDN,aACAvD,YAAYwC,WAAW,EACtB;gBACD,uFAAuF;gBACvF,MAAMsB,eAAe,AACnBF,WAAWG,UAAU,CACrBC,OAAO;gBAET,MAAM,EAAEC,sBAAsB,EAAEC,aAAa,EAAEnF,UAAU,EAAE,GACzD,IAAI,CAACoF,6CAA6C,CAAC;oBACjDL;oBACA9D;oBACAoE,gBAAgBR,WAAWQ,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM,GACjCd,mBAAmBrD,GAAG,CAACkE,KAAKC;gBAG9B,MAAMC,oBAAoBpG,aAAI,CAACqG,UAAU,CAACX;gBAE1C,mDAAmD;gBACnD,IAAI,CAACU,mBAAmB;oBACtBhH,OAAOkH,IAAI,CAACT,wBAAwBI,OAAO,CACzC,CAACM,QAAWnB,mCAAmC,CAACmB,MAAM,GAAG,IAAI9F;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAM+F,kBAAkBJ,oBACpBpG,aAAI,CAAC4D,QAAQ,CAAChC,YAAYT,OAAO,CAAC0C,OAAO,EAAG6B,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,MAAMe,aAAa3C,IAAAA,kCAAgB,EACjC0C,gBAAgBzC,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlE3E,OAAOsH,MAAM,CAACxH,kBAAkByB;gBAChC4E,sBAAsBtE,IAAI,CAAC;oBACzBS;oBACAE;oBACAlB,WAAWR;oBACX2F;oBACAY;oBACAE,kBAAkBjB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACExF,SAAS,CAAC,GAAG,EAAE0G,4CAAgC,CAAC,CAAC,IACjDH,eAAe,iBACf;oBACAlB,sBAAsBtE,IAAI,CAAC;wBACzBS;wBACAE;wBACAlB,WAAWR;wBACX2F,wBAAwB,CAAC;wBACzBY,YAAY,CAAC,GAAG,EAAEG,4CAAgC,CAAC,CAAC;wBACpDD,kBAAkBjB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAMnF,oBAAoBtB,8BAA8BC;YACxD,KAAK,MAAM2H,uBAAuBtB,sBAAuB;gBACvD,MAAMuB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBhB,sBAAsB;wBAC7C,GAAG,AACDtF,CAAAA,iBAAiB,CAACsG,oBAAoBF,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DM,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAI1G;4BAChB,OAAOyG;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACpC,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,EAAE;oBAClEoE,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACAoE,8BAA8B,CAAC+B,oBAAoBnG,SAAS,CAAC,CAACO,IAAI,CAChE6F,QAAQ,CAAC,EAAE;gBAGbjC,gCAAgC5D,IAAI,CAAC6F;YACvC;YAEA,sBAAsB;YACtBjC,gCAAgC5D,IAAI,CAClC,IAAI,CAAC8F,8BAA8B,CAAC;gBAClCrF;gBACAE;gBACAlB,WAAWR;gBACX8G,eAAe;oBAAE,GAAG5B,mCAAmC;gBAAC;gBACxDqB,YAAYW,gCAAoB;YAClC;YAGF,IAAI/B,mBAAmBgC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACrC,kBAAkB,CAAC9E,KAAK,EAAE;oBAC7B8E,kBAAkB,CAAC9E,KAAK,GAAG,IAAIoF;gBACjC;gBACAN,kBAAkB,CAAC9E,KAAK,GAAG,IAAIoF,IAAI;uBAC9BN,kBAAkB,CAAC9E,KAAK;uBACxBmF;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACnF,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrD2F,oBACC;YACD,KAAK,MAAM,CAACkB,KAAKoB,YAAY,IAAIjC,mBAAoB;gBACnD,KAAK,MAAMkC,cAAcD,YAAa;oBACpCrC,eAAejE,GAAG,CAACd,OAAO,MAAMgG,MAAM,MAAMqB;gBAC9C;YACF;YACAxC,mBAAmB9D,IAAI,CACrB,IAAI,CAACuG,iBAAiB,CAAC;gBACrB9F;gBACAE;gBACA6F,SAASpC;gBACT3E,WAAWR;gBACXuG,YAAYvG;YACd;QAEJ;QAEA,qDAAqD;QACrD,MAAMwH,cAAcC,IAAAA,oCAAc,EAACjG,SAASkG,UAAU;QACtD,4DAA4D;QAC5D,IACEF,eACA7C,gCAAgCgD,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAJ,YAAYK,UAAU,CAAC;gBAACC,0BAAc,CAACC,MAAM;aAAC;QAChD;QAEA,qGAAqG;QACrG,6EAA6E;QAC7E,MAAMC,QAAQC,GAAG,CACftD,gCAAgCuD,GAAG,CACjC,CAACC,8BAAgCA,2BAA2B,CAAC,EAAE;QAInE,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAACpD;QAElB,MAAMuD,6BAA6C,EAAE;QACrD,MAAMC,2BAAkE,CAAC;QAEzE,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAACrI,MAAMsI,qBAAqB,IAAIpJ,OAAOC,OAAO,CACvDyF,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMO,qBAAqB,IAAI,CAACoD,oCAAoC,CAAC;gBACnE7G;gBACAM,cAAcsG;YAChB;YAEA,IAAInD,mBAAmBgC,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACkB,wBAAwB,CAACrI,KAAK,EAAE;oBACnCqI,wBAAwB,CAACrI,KAAK,GAAG,IAAIoF;gBACvC;gBACAiD,wBAAwB,CAACrI,KAAK,GAAG,IAAIoF,IAAI;uBACpCiD,wBAAwB,CAACrI,KAAK;uBAC9BmF;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAACnF,MAAMmF,mBAAmB,IAAIjG,OAAOC,OAAO,CACrDkJ,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAIG,iCAAiC;YACrC,MAAMC,8BAA8B,IAAIrD;YACxC,KAAK,MAAM,CAACY,KAAKoB,YAAY,IAAIjC,mBAAoB;gBACnD,MAAMuD,uBAAuB,EAAE;gBAC/B,KAAK,MAAMrB,cAAcD,YAAa;oBACpC,MAAMuB,KAAK3I,OAAO,MAAMgG,MAAM,MAAMqB;oBACpC,IAAI,CAACtC,eAAepE,GAAG,CAACgI,KAAK;wBAC3BD,qBAAqB3H,IAAI,CAACsG;oBAC5B;gBACF;gBACA,IAAIqB,qBAAqB/I,MAAM,GAAG,GAAG;oBACnC8I,4BAA4B3G,GAAG,CAACkE,KAAK0C;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCJ,2BAA2BrH,IAAI,CAC7B,IAAI,CAACuG,iBAAiB,CAAC;oBACrB9F;oBACAE;oBACA6F,SAASkB;oBACTjI,WAAWR;oBACXuG,YAAYvG;oBACZ4I,YAAY;gBACd;YAEJ;QACF;QAEA,MAAMZ,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnC7G,WAAW,EACXM,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAM6G,mBAAmB,IAAIzD;QAE7B,gFAAgF;QAChF,MAAM0D,gBAAgB,IAAIvI;QAC1B,MAAMwI,eAAe,IAAIxI;QAEzB,MAAMyI,iBAAiB,CAAC,EACtBxD,YAAY,EACZM,cAAc,EAIf;YACC,MAAMmD,sBAAsB,CAACtG;gBAC3B,IAAI,CAACA,KAAK;gBAEV,MAAMM,cAAciG,kBAAkBvG;gBAEtC,IAAI,CAACM,eAAe6F,cAAcnI,GAAG,CAACsC,cAAc;gBACpD6F,cAAchI,GAAG,CAACmC;gBAElB,MAAMsE,UAAU4B,IAAAA,8BAAuB,EAACxG;gBACxC,IAAI4E,SAAS;oBACXsB,iBAAiB/G,GAAG,CAACmB,aAAasE;gBACpC;gBAEAhC,IAAAA,kCAA0B,EAAC5C,KAAKjB,YAAYwC,WAAW,EAAE6B,OAAO,CAC9D,CAACT;oBACC2D,oBACE3D,WAAWQ,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEN,gBACA,CAACA,aAAa3E,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3DoI,oBAAoBnD;YACtB;QACF;QAEA,KAAK,MAAMsD,mBAAmBpH,aAAc;YAC1C,MAAMqH,iBACJ3H,YAAYwC,WAAW,CAACoF,iBAAiB,CAACF;YAC5C,KAAK,MAAM9D,cAAcC,IAAAA,kCAA0B,EACjD8D,gBACA3H,YAAYwC,WAAW,EACtB;gBACD,MAAMqF,YAAYjE,WAAWG,UAAU;gBACvC,MAAMC,UAAU,AAAC6D,UAA8C7D,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIqD,aAAapI,GAAG,CAAC+E,UAAU;gBAC/BqD,aAAajI,GAAG,CAAC4E;gBAEjBsD,eAAe;oBACbxD,cAAcE;oBACdI,gBAAgBR,WAAWQ,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO+C;IACT;IAEAhD,8CAA8C,EAC5CL,YAAY,EACZ9D,WAAW,EACXoE,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAM0D,UAAU,IAAIjJ;QAEpB,mBAAmB;QACnB,MAAMoF,yBAAiD,CAAC;QACxD,MAAMC,gBAAsC,EAAE;QAC9C,MAAM6D,aAAa,IAAIlJ;QAEvB,MAAMmJ,yBAAyB,CAC7B/G,KACAgH;YAEA,IAAI,CAAChH,KAAK;YAEV,MAAMiH,QAAQC,IAAAA,eAAQ,EAAClH;YACvB,MAAMM,cAAciG,kBAAkBvG;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAIuG,QAAQ7I,GAAG,CAACsC,cAAc;gBAC5B,IAAI0C,sBAAsB,CAAC1C,YAAY,EAAE;oBACvC6G,gBACEnH,KACAM,aACA0C,wBACAgE,qBACA;gBAEJ;gBACA;YACF;YACAH,QAAQ1I,GAAG,CAACmC;YAEZ,MAAMsE,UAAU4B,IAAAA,8BAAuB,EAACxG;YACxC,IAAI4E,SAAS;gBACX3B,cAAc7E,IAAI,CAAC;oBAACkC;oBAAasE;iBAAQ;YAC3C;YAEA,MAAMwC,iBAAiB,IAAI,CAAC3I,YAAY,GACpC4I,gCAAoB,GACpBC,mCAAuB;YAE3B,IAAIL,OAAO;gBACT,MAAMM,iBACJvH,IAAIwH,WAAW,IAAI,AAACxH,IAAIwH,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAAC1I,YAAYwC,WAAW,CACpCmG,cAAc,CAAC1H,KACf2H,YAAY,CAACP;oBAEhB,IAAIK,QAAQ;gBACd;gBAEAX,WAAW3I,GAAG,CAACmC;YACjB,OAAO,IAAIsH,IAAAA,mCAA4B,EAAC5H,MAAM;gBAC5C,IAAI,CAACgD,sBAAsB,CAAC1C,YAAY,EAAE;oBACxC0C,sBAAsB,CAAC1C,YAAY,GAAG,IAAI1C;gBAC5C;gBACAuJ,gBACEnH,KACAM,aACA0C,wBACAgE,qBACA;gBAGF;YACF;YAEApE,IAAAA,kCAA0B,EAAC5C,KAAKjB,YAAYwC,WAAW,EAAE6B,OAAO,CAC9D,CAACT;oBAKKA;gBAJJ,IAAIkF,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAIlF,yBAAAA,WAAWG,UAAU,qBAArBH,uBAAuBmF,GAAG,EAAE;oBAC9BD,cAAczJ,IAAI,IAAIuE,WAAWG,UAAU,CAACgF,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAd,uBAAuBpE,WAAWQ,cAAc,EAAE0E;YACpD;QAEJ;QAEA,2DAA2D;QAC3Dd,uBAAuB5D,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACAlF,YAAYgJ,WAAWtC,IAAI,GACvB;gBACE,CAAC3B,aAAa,EAAEkF,MAAMC,IAAI,CAAClB;YAC7B,IACA,CAAC;YACL7D;QACF;IACF;IAEAiB,+BAA+B,EAC7BrF,QAAQ,EACRE,WAAW,EACXlB,SAAS,EACTsG,aAAa,EACbP,UAAU,EACVE,gBAAgB,EAQjB,EAIC;QACA,IAAImB,mBAAmB;QAEvB,MAAMgD,gBAGF;YACFC,SAAS3L,OAAOkH,IAAI,CAACU,eAClB1H,IAAI,CAAC,CAACC,GAAGC,IAAOwL,eAAQ,CAACC,IAAI,CAACzL,KAAK,IAAID,EAAE2L,aAAa,CAAC1L,IACvD4I,GAAG,CAAC,CAAC+C,mBAAsB,CAAA;oBAC1BvF,SAASuF;oBACTR,KAAK;2BAAI3D,aAAa,CAACmE,iBAAiB;qBAAC;gBAC3C,CAAA;YACFC,QAAQ;QACV;QAEA,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEC,IAAAA,sBAAS,EAAC;YACvEP,SAAS,AAAC,CAAA,IAAI,CAACzJ,YAAY,GACvBwJ,cAAcC,OAAO,CAAC3C,GAAG,CAAC,CAAC,EAAExC,OAAO,EAAE+E,GAAG,EAAE,GAAM,CAAA;oBAC/C/E,SAASA,QAAQ7B,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAO/D,aAAI,CAACuL,GAAG;oBAEvCZ;gBACF,CAAA,KACAG,cAAcC,OAAO,AAAD,EACtB3C,GAAG,CAAC,CAACoD,IAAMC,KAAKH,SAAS,CAACE;YAC5BJ,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMM,kBAAkB,CAAC,gCAAgC,EAAEJ,IAAAA,sBAAS,EAAC;YACnEP,SAASD,cAAcC,OAAO,CAAC3C,GAAG,CAAC,CAACoD,IAAMC,KAAKH,SAAS,CAACE;YACzDJ,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAChK,GAAG,EAAE;YACZ,MAAM/B,UAAUsM,IAAAA,gCAAU,EAACjK,SAASkG,UAAU;YAC9C,MAAMgE,UAAUC,IAAAA,iCAAW,EACzB7D,0BAAc,CAACC,MAAM,EACrB6D,qBAAU,CAACC,GAAG,EACdtF;YAGF,IAAI,CAACpH,OAAO,CAACuM,QAAQ,EAAE;gBACrBvM,OAAO,CAACuM,QAAQ,GAAG;oBACjBI,MAAMC,gCAAU,CAACC,WAAW;oBAC5BC,eAAe,IAAI1L,IAAI;wBAACC;qBAAU;oBAClC0L,uBAAuBzF;oBACvBF;oBACAb,SAASyF;oBACTgB,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACA1E,mBAAmB;YACrB,OAAO;gBACL,MAAM2E,YAAYpN,OAAO,CAACuM,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIa,UAAU7G,OAAO,KAAKyF,qBAAqB;oBAC7CoB,UAAU7G,OAAO,GAAGyF;oBACpBvD,mBAAmB;gBACrB;gBACA,IAAI2E,UAAUT,IAAI,KAAKC,gCAAU,CAACC,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAACnL,GAAG,CAACN;gBAC9B;gBACA+L,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLjO,YAAYS,qBAAqB,CAACyH,WAAW,GAAG4E;QAClD;QAEA,qDAAqD;QACrD,MAAMqB,0BAA0BzK,gBAAO,CAAC0K,WAAW,CAACC,gBAAgB,CAClElB,iBACA;YACExL,MAAMuG;QACR;QAGF,OAAO;YACLqB;YACA,6CAA6C;YAC7C,gGAAgG;YAChG,qEAAqE;YACrE,IAAI,CAAC+E,QAAQ,CACXjL,aACA,6BAA6B;YAC7BF,SAASmC,OAAO,EAChB6I,yBACA;gBACE,+BAA+B;gBAC/BxM,MAAMQ;gBACN,6CAA6C;gBAC7C,iEAAiE;gBACjE8C,OAAOC,yBAAc,CAACC,mBAAmB;YAC3C;YAEFgJ;SACD;IACH;IAEAlF,kBAAkB,EAChB9F,QAAQ,EACRE,WAAW,EACX6F,OAAO,EACP/G,SAAS,EACT+F,UAAU,EACVqC,UAAU,EAQX,EAAE;QACD,MAAMgE,eAAelC,MAAMC,IAAI,CAACpD,QAAQpI,OAAO;QAE/C,MAAM0N,eAAe,CAAC,gCAAgC,EAAEzB,IAAAA,sBAAS,EAAC;YAChE7D,SAASgE,KAAKH,SAAS,CAACwB;YACxBE,qBAAqBlE;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMmE,+BAA+B,IAAI,CAAC3L,YAAY,GAClD/C,YAAYG,iBAAiB,GAC7BH,YAAYE,aAAa;QAC7B,KAAK,MAAM,CAACyO,GAAG/G,MAAM,IAAI2G,aAAc;YACrC,KAAK,MAAM5M,QAAQiG,MAAO;gBACxB,MAAM0C,KAAKsE,IAAAA,uBAAgB,EAACD,GAAGhN;gBAC/B,IAAI,OAAO+M,4BAA4B,CAACpE,GAAG,KAAK,aAAa;oBAC3DoE,4BAA4B,CAACpE,GAAG,GAAG;wBACjCuE,SAAS,CAAC;wBACV5J,OAAO,CAAC;oBACV;gBACF;gBACAyJ,4BAA4B,CAACpE,GAAG,CAACuE,OAAO,CAAC3G,WAAW,GAAG;gBACvDwG,4BAA4B,CAACpE,GAAG,CAACrF,KAAK,CAACiD,WAAW,GAAGqC,aACjDrF,yBAAc,CAAC4J,aAAa,GAC5B5J,yBAAc,CAAC6J,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMC,iBAAiBtL,gBAAO,CAAC0K,WAAW,CAACC,gBAAgB,CAACG,cAAc;YACxE7M,MAAMuG;QACR;QAEA,OAAO,IAAI,CAACoG,QAAQ,CAClBjL,aACA,6BAA6B;QAC7BF,SAASmC,OAAO,EAChB0J,gBACA;YACErN,MAAMQ;YACN8C,OAAOsF,aACHrF,yBAAc,CAAC4J,aAAa,GAC5B5J,yBAAc,CAAC6J,qBAAqB;QAC1C;IAEJ;IAEAT,SACEjL,WAAgB,EAChBiC,OAAe,EACf8B,UAA8B,EAC9BxE,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAI+G,QAAQ,CAACsF,SAASC;YAC3B,MAAMC,QAAQ9L,YAAYvC,OAAO,CAACsO,GAAG,CAACxM,QAAQjB,IAAI;YAClDwN,MAAME,mBAAmB,CAAC3M,IAAI,CAAC0E;YAC/B/D,YAAYD,KAAK,CAACkL,QAAQ,CAACgB,IAAI,CAACH,OAAOvM;YACvCS,YAAYkM,aAAa,CACvB;gBACEjK;gBACA8B;gBACAoI,aAAa;oBAAEC,aAAa7M,QAAQqC,KAAK;gBAAC;YAC5C,GACA,CAACyK,KAAwBC;gBACvB,IAAID,KAAK;oBACPrM,YAAYD,KAAK,CAACwM,WAAW,CAACN,IAAI,CAAClI,YAAYxE,SAAS8M;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEArM,YAAYD,KAAK,CAACyM,YAAY,CAACP,IAAI,CAAClI,YAAYxE,SAAS+M;gBACzD,OAAOV,QAAQU;YACjB;QAEJ;IACF;IAEA,MAAMtJ,mBACJhD,WAAgC,EAChC+C,MAAqC,EACrC;QACA,MAAMlG,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDsF,IAAAA,uBAAe,EAACpC,aAAa,CAACiB,KAAKoB,QAAQoK,YAAYzL;YACrD,yEAAyE;YACzE,IACEyL,WAAWnO,IAAI,IACf2C,IAAI+C,OAAO,IACXhD,SACA,kCAAkCqI,IAAI,CAACpI,IAAI+C,OAAO,GAClD;gBACA,MAAMkD,aAAa,4BAA4BmC,IAAI,CAACpI,IAAI+C,OAAO;gBAE/D,MAAM0I,UAAU,IAAI,CAAChN,YAAY,GAC7B/C,YAAYK,qBAAqB,GACjCL,YAAYI,iBAAiB;gBAEjC,IAAI,CAAC2P,OAAO,CAACD,WAAWnO,IAAI,CAAC,EAAE;oBAC7BoO,OAAO,CAACD,WAAWnO,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACAoO,OAAO,CAACD,WAAWnO,IAAI,CAAC,CAAC4I,aAAa,WAAW,SAAS,GAAGlG;YAC/D;QACF;QAEA,IAAK,IAAIiG,MAAMtK,YAAYE,aAAa,CAAE;YACxC,MAAM8P,SAAShQ,YAAYE,aAAa,CAACoK,GAAG;YAC5C,IAAK,IAAI3I,QAAQqO,OAAOnB,OAAO,CAAE;gBAC/B,MAAMxK,QACJrE,YAAYI,iBAAiB,CAACuB,KAAK,CACjCqO,OAAO/K,KAAK,CAACtD,KAAK,KAAKuD,yBAAc,CAAC4J,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAClN,KAAK,GAAG0C;YACzB;YACAnE,aAAa,CAACoK,GAAG,GAAG0F;QACtB;QAEA,IAAK,IAAI1F,MAAMtK,YAAYG,iBAAiB,CAAE;YAC5C,MAAM6P,SAAShQ,YAAYG,iBAAiB,CAACmK,GAAG;YAChD,IAAK,IAAI3I,QAAQqO,OAAOnB,OAAO,CAAE;gBAC/B,MAAMxK,QACJrE,YAAYK,qBAAqB,CAACsB,KAAK,CACrCqO,OAAO/K,KAAK,CAACtD,KAAK,KAAKuD,yBAAc,CAAC4J,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAClN,KAAK,GAAG0C;YACzB;YACAlE,iBAAiB,CAACmK,GAAG,GAAG0F;QAC1B;QAEA,MAAMC,iBAAiB;YACrBC,MAAMhQ;YACNiQ,MAAMhQ;YACN8C,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAMmN,qBAAqB;YACzB,GAAGH,cAAc;YACjBhN,eAAe;QACjB;QAEA,MAAMoN,OAAOnD,KAAKH,SAAS,CAACkD,gBAAgB,MAAM,IAAI,CAACpN,GAAG,GAAG,IAAIyN;QACjE,MAAMC,WAAWrD,KAAKH,SAAS,CAC7BqD,oBACA,MACA,IAAI,CAACvN,GAAG,GAAG,IAAIyN;QAGjBlK,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEwN,qCAAyB,CAAC,GAAG,CAAC,CAAC,GAC1D,IAAIC,gBAAO,CAACC,SAAS,CACnB,CAAC,2BAA2B,EAAExD,KAAKH,SAAS,CAACwD,UAAU,CAAC;QAE5DnK,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpD,WAAW,CAAC,EAAEwN,qCAAyB,CAAC,KAAK,CAAC,CAAC,GAC5D,IAAIC,gBAAO,CAACC,SAAS,CAACL;IAC1B;AACF;AAEA,SAAS5E,gBACPnH,GAAyB,EACzBqM,UAAkB,EAClBrJ,sBAA8C,EAC9CgE,mBAA6B,EAC7BsF,kBAA2B;QAEHC;IAAxB,MAAMC,mBAAkBD,0BAAAA,IAAAA,sCAAkB,EAACvM,KAAKyM,GAAG,qBAA3BF,wBAA6BC,eAAe;IACpE,MAAME,cAAcF,oBAAoB;IACxC,MAAMG,oBAAoBC,IAAAA,sCAAoB,EAC5C5M,KACA0M,cAAc,aAAa;IAG7B,MAAMG,mBAAmB7J,sBAAsB,CAACqJ,WAAW;IAE3D,IAAIrF,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACsF,sBAAsB;eAAIO;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3D7J,sBAAsB,CAACqJ,WAAW,GAAG,IAAIzO,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMkP,yBAAyBH,sBAAsB;QACrD,IAAIG,wBAAwB;YAC1B9J,sBAAsB,CAACqJ,WAAW,GAAG,IAAIzO,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQ2J,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAM+F,qBAAqBL,eAAerP,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAI0P,oBAAoB;oBACtB/J,sBAAsB,CAACqJ,WAAW,CAAClO,GAAG,CAAC;gBACzC;gBAEA6E,sBAAsB,CAACqJ,WAAW,CAAClO,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAASkJ,kBAAkBvG,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyB7C,IAAI,KAAI;IACzD,MAAMiD,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAI3B,WAAW,CAAChB,IAAI,KAAK,iBAAiB;QAC5CiD,cAAcN,IAAIgN,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAIhN,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACC,sCAA0B,GAAG;QAC7DF,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IACA,OAAOA;AACT"}