{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-font-manifest-plugin.ts"], "names": ["NextFontManifestPlugin", "PLUGIN_NAME", "getPreloadedFontFiles", "fontFiles", "filter", "file", "test", "getPageIsUsingSizeAdjust", "some", "includes", "constructor", "options", "appDir", "apply", "compiler", "hooks", "make", "tap", "compilation", "processAssets", "name", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "assets", "nextFontManifest", "pages", "app", "appUsingSizeAdjust", "pagesUsingSizeAdjust", "appDirBase", "path", "dirname", "sep", "traverseModules", "mod", "_chunk", "chunkGroup", "request", "buildInfo", "chunkEntryName", "replace", "modAssets", "Object", "keys", "preloadedFontFiles", "length", "push", "startsWith", "entrypoint", "entrypoints", "values", "pagePath", "getRouteFromEntrypoint", "chunks", "flatMap", "chunk", "auxiliaryFiles", "manifest", "JSON", "stringify", "NEXT_FONT_MANIFEST", "sources", "RawSource"], "mappings": ";;;;+BAsDaA;;;eAAAA;;;yBAtDoB;+EACE;2BACA;uBACH;6DACf;;;;;;AAYjB,MAAMC,cAAc;AAEpB;;;;;;CAMC,GACD,SAASC,sBAAsBC,SAAmB;IAChD,OAAOA,UAAUC,MAAM,CAAC,CAACC,OACvB,iCAAiCC,IAAI,CAACD;AAE1C;AAEA;;;;CAIC,GACD,SAASE,yBAAyBJ,SAAmB;IACnD,OAAOA,UAAUK,IAAI,CAAC,CAACH,OAASA,KAAKI,QAAQ,CAAC;AAChD;AAgBO,MAAMT;IAGXU,YAAYC,OAAuC,CAAE;QACnD,IAAI,CAACC,MAAM,GAAGD,QAAQC,MAAM;IAC9B;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAChB,aAAa,CAACiB;YACpC,4GAA4G;YAC5GA,YAAYH,KAAK,CAACI,aAAa,CAACF,GAAG,CACjC;gBACEG,MAAMnB;gBACNoB,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC;gBACC,MAAMC,mBAAqC;oBACzCC,OAAO,CAAC;oBACRC,KAAK,CAAC;oBACNC,oBAAoB;oBACpBC,sBAAsB;gBACxB;gBAEA,IAAI,IAAI,CAAClB,MAAM,EAAE;oBACf,MAAMmB,aAAaC,aAAI,CAACC,OAAO,CAAC,IAAI,CAACrB,MAAM,IAAIoB,aAAI,CAACE,GAAG;oBAEvD,8FAA8F;oBAC9FC,IAAAA,sBAAe,EACbjB,aACA,CAACkB,KAAKC,QAAQC;4BACRF;wBAAJ,IAAIA,wBAAAA,eAAAA,IAAKG,OAAO,qBAAZH,aAAc3B,QAAQ,CAAC,gCAAgC;gCACpD2B;4BAAL,IAAI,GAACA,iBAAAA,IAAII,SAAS,qBAAbJ,eAAeX,MAAM,GAAE;4BAE5B,MAAMgB,iBAAiB,AAACV,CAAAA,aAAaO,WAAWlB,IAAI,AAAD,EAAGsB,OAAO,CAC3D,UACAV,aAAI,CAACE,GAAG;4BAGV,MAAMS,YAAYC,OAAOC,IAAI,CAACT,IAAII,SAAS,CAACf,MAAM;4BAClD,MAAMtB,YAAsBwC,UAAUvC,MAAM,CAAC,CAACC,OAC5C,8BAA8BC,IAAI,CAACD;4BAGrC,kDAAkD;4BAClD,IAAI,CAACqB,iBAAiBG,kBAAkB,EAAE;gCACxCH,iBAAiBG,kBAAkB,GACjCtB,yBAAyBJ;4BAC7B;4BAEA,MAAM2C,qBAAqB5C,sBAAsBC;4BAEjD,2DAA2D;4BAC3D,sDAAsD;4BACtD,uGAAuG;4BACvG,IAAIA,UAAU4C,MAAM,GAAG,GAAG;gCACxB,IAAI,CAACrB,iBAAiBE,GAAG,CAACa,eAAe,EAAE;oCACzCf,iBAAiBE,GAAG,CAACa,eAAe,GAAG,EAAE;gCAC3C;gCACAf,iBAAiBE,GAAG,CAACa,eAAe,CAACO,IAAI,IACpCF;4BAEP;wBACF;oBACF,GACA,CAACR;4BAEUA;wBADT,qDAAqD;wBACrD,OAAO,CAAC,GAACA,mBAAAA,WAAWlB,IAAI,qBAAfkB,iBAAiBW,UAAU,CAAC;oBACvC;gBAEJ;gBAEA,kDAAkD;gBAClD,KAAK,MAAMC,cAAchC,YAAYiC,WAAW,CAACC,MAAM,GAAI;oBACzD,MAAMC,WAAWC,IAAAA,+BAAsB,EAACJ,WAAW9B,IAAI;oBAEvD,IAAI,CAACiC,UAAU;wBACb;oBACF;oBAEA,6DAA6D;oBAC7D,MAAMlD,YAAsB+C,WAAWK,MAAM,CAC1CC,OAAO,CAAC,CAACC,QAAe;+BAAIA,MAAMC,cAAc;yBAAC,EACjDtD,MAAM,CAAC,CAACC,OACP,8BAA8BC,IAAI,CAACD;oBAGvC,kDAAkD;oBAClD,IAAI,CAACqB,iBAAiBI,oBAAoB,EAAE;wBAC1CJ,iBAAiBI,oBAAoB,GACnCvB,yBAAyBJ;oBAC7B;oBAEA,MAAM2C,qBAAqB5C,sBAAsBC;oBAEjD,0DAA0D;oBAC1D,sDAAsD;oBACtD,uGAAuG;oBACvG,IAAIA,UAAU4C,MAAM,GAAG,GAAG;wBACxBrB,iBAAiBC,KAAK,CAAC0B,SAAS,GAAGP;oBACrC;gBACF;gBAEA,MAAMa,WAAWC,KAAKC,SAAS,CAACnC,kBAAkB;gBAClD,2BAA2B;gBAC3BD,MAAM,CAAC,CAAC,OAAO,EAAEqC,6BAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAIC,gBAAO,CAACC,SAAS,CAC/D,CAAC,0BAA0B,EAAEJ,KAAKC,SAAS,CAACF,UAAU,CAAC;gBAEzD,6BAA6B;gBAC7BlC,MAAM,CAAC,CAAC,OAAO,EAAEqC,6BAAkB,CAAC,KAAK,CAAC,CAAC,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACjEL;YAEJ;QAEJ;QACA;IACF;AACF"}