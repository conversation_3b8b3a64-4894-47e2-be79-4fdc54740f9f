{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/getCssModuleLocalIdent.ts"], "names": ["getCssModuleLocalIdent", "regexLikeIndexModule", "context", "_", "exportName", "options", "relativePath", "path", "relative", "rootContext", "resourcePath", "replace", "fileNameOrFolder", "test", "hash", "loaderUtils", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "interpolateName"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;qEANQ;6DACP;;;;;;AAGjB,MAAMC,uBAAuB;AAEtB,SAASD,uBACdE,OAAkC,EAClCC,CAAM,EACNC,UAAkB,EAClBC,OAAe;IAEf,MAAMC,eAAeC,aAAI,CACtBC,QAAQ,CAACN,QAAQO,WAAW,EAAEP,QAAQQ,YAAY,EAClDC,OAAO,CAAC,QAAQ;IAEnB,0EAA0E;IAC1E,2BAA2B;IAC3B,MAAMC,mBAAmBX,qBAAqBY,IAAI,CAACP,gBAC/C,aACA;IAEJ,iDAAiD;IACjD,MAAMQ,OAAOC,qBAAW,CAACC,aAAa,CACpCC,OAAOC,IAAI,CAAC,CAAC,SAAS,EAAEZ,aAAa,WAAW,EAAEF,WAAW,CAAC,GAC9D,QACA,UACA;IAGF,yEAAyE;IACzE,OACEW,qBAAW,CACRI,eAAe,CACdjB,SACAU,mBAAmB,MAAMR,aAAa,OAAOU,MAC7CT,SAEDM,OAAO,CACN,oEAAoE;IACpE,oEAAoE;IACpE,QAAQ;IACR,aACA,IAEF,+DAA+D;IAC/D,iEAAiE;KAChEA,OAAO,CAAC,mBAAmB,IAC5B,uFAAuF;IACvF,sDAAsD;KACrDA,OAAO,CAAC,gBAAgB;AAE/B"}