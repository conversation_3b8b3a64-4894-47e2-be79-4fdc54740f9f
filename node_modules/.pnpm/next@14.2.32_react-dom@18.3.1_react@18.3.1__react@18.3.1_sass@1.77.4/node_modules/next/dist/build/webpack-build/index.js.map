{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["webpackBuild", "debug", "origDebug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "NextBuildContext", "combinedResult", "duration", "buildTraceContext", "compilerName", "curR<PERSON>ult", "worker", "Worker", "path", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "worker<PERSON>ain", "buildContext", "traceState", "exportTraceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "recordTraceEvents", "end", "telemetryState", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "Log", "event", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": ";;;;+BAiHgBA;;;eAAAA;;;6DAhHK;8BACY;wBAEV;8DACD;6DACL;uBACmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEpD,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAExB,MAAMC,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGC,8BAAgB;IAEjED,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMgB,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAMC,gBAAgBP,cAAe;YAsCpCQ;QArCJ,MAAMC,SAAS,IAAIC,cAAM,CAACC,aAAI,CAACC,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QAEA,MAAMZ,YAAY,MAAMC,OAAOY,UAAU,CAAC;YACxCC,cAAcpB;YACdK;YACAgB,YAAY;gBACV,GAAGC,IAAAA,uBAAgB,GAAE;gBACrBC,mBAAmB,EAAExB,iCAAAA,cAAeyB,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAI1B,iBAAiBO,UAAUoB,gBAAgB,EAAE;YAC/CC,IAAAA,wBAAiB,EAACrB,UAAUoB,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMnB,OAAOqB,GAAG;QAEhB,sBAAsB;QACtB1C,cAAcC,UAAUD,aAAaoB,UAAUpB,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAIoB,UAAUuB,cAAc,EAAE;YAC5B5B,8BAAgB,CAAC4B,cAAc,GAAGvB,UAAUuB,cAAc;QAC5D;QAEA3B,eAAeC,QAAQ,IAAIG,UAAUH,QAAQ;QAE7C,KAAIG,+BAAAA,UAAUF,iBAAiB,qBAA3BE,6BAA6BwB,YAAY,EAAE;gBAUzCxB;YATJ,MAAM,EAAEyB,YAAY,EAAE,GAAGzB,UAAUF,iBAAiB,CAAC0B,YAAY;YAEjE,IAAIC,cAAc;gBAChB7B,eAAeE,iBAAiB,CAAC0B,YAAY,GAC3CxB,UAAUF,iBAAiB,CAAC0B,YAAY;gBAC1C5B,eAAeE,iBAAiB,CAAC0B,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIzB,gCAAAA,UAAUF,iBAAiB,qBAA3BE,8BAA6B0B,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAG3B,UAAUF,iBAAiB,CAAC4B,WAAW;gBAErE,IAAIC,mBAAmB;oBACrB/B,eAAeE,iBAAiB,CAAC4B,WAAW,GAC1C1B,UAAUF,iBAAiB,CAAC4B,WAAW;oBAEzC9B,eAAeE,iBAAiB,CAAC4B,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAInC,cAAcoC,MAAM,KAAK,GAAG;QAC9BC,KAAIC,KAAK,CAAC;IACZ;IAEA,OAAOlC;AACT;AAEO,SAASpB,aACduD,UAAmB,EACnBvC,aAAmD;IAEnD,IAAIuC,YAAY;QACdtD,MAAM;QACN,OAAOa,uBAAuBE;IAChC,OAAO;QACLf,MAAM;QACN,MAAMuD,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA,iBAAiB,MAAM;IAChC;AACF"}