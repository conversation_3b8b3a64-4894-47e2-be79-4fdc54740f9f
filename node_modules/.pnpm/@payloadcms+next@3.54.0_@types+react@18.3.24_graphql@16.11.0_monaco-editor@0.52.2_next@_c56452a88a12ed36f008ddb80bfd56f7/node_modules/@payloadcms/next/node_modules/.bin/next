#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/bin/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/bin/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules:/Users/<USER>/Dev/rotary/CMS_V2/rotary-cms/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/bin/next" "$@"
else
  exec node  "$basedir/../../../../../../next@14.2.32_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.77.4/node_modules/next/dist/bin/next" "$@"
fi
