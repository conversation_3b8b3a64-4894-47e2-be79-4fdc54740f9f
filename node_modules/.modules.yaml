hoistPattern:
  - '*'
hoistedDependencies:
  '@apidevtools/json-schema-ref-parser@11.9.3':
    '@apidevtools/json-schema-ref-parser': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/runtime@7.28.3':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@borewit/text-codec@0.1.1':
    '@borewit/text-codec': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@date-fns/tz@1.2.0':
    '@date-fns/tz': private
  '@dnd-kit/accessibility@3.1.1(react@18.3.1)':
    '@dnd-kit/accessibility': private
  '@dnd-kit/core@6.0.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/core': private
  '@dnd-kit/sortable@7.0.2(@dnd-kit/core@6.0.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)':
    '@dnd-kit/sortable': private
  '@dnd-kit/utilities@3.2.2(react@18.3.1)':
    '@dnd-kit/utilities': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@18.3.24)(react@18.3.1)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.10.0':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/darwin-arm64@0.25.9':
    '@esbuild/darwin-arm64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@faceless-ui/modal@3.0.0-beta.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@faceless-ui/modal': private
  '@faceless-ui/scroll-info@2.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@faceless-ui/scroll-info': private
  '@faceless-ui/window-info@3.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@faceless-ui/window-info': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.4':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.27.16(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@monaco-editor/react@4.7.0(monaco-editor@0.52.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@monaco-editor/react': private
  '@mongodb-js/saslprep@1.3.0':
    '@mongodb-js/saslprep': private
  '@next/env@14.2.32':
    '@next/env': private
  '@next/eslint-plugin-next@14.1.0':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@14.2.32':
    '@next/swc-darwin-arm64': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@payloadcms/graphql@3.54.0(graphql@16.11.0)(payload@3.54.0(graphql@16.11.0)(typescript@5.9.2))(typescript@5.9.2)':
    '@payloadcms/graphql': private
  '@payloadcms/translations@3.54.0':
    '@payloadcms/translations': private
  '@payloadcms/ui@3.54.0(@types/react@18.3.24)(monaco-editor@0.52.2)(next@14.2.32(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(sass@1.77.4))(payload@3.54.0(graphql@16.11.0)(typescript@5.9.2))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.9.2)':
    '@payloadcms/ui': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.5':
    '@swc/helpers': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/busboy@1.5.4':
    '@types/busboy': private
  '@types/is-hotkey@0.1.10':
    '@types/is-hotkey': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/react-transition-group@4.4.12(@types/react@18.3.24)':
    '@types/react-transition-group': private
  '@types/webidl-conversions@7.0.3':
    '@types/webidl-conversions': private
  '@types/whatwg-url@11.0.5':
    '@types/whatwg-url': private
  '@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  balanced-match@1.0.2:
    balanced-match: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-scroll-lock@4.0.0-beta.0:
    body-scroll-lock: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bson-objectid@2.0.4:
    bson-objectid: private
  bson@6.10.4:
    bson: private
  busboy@1.6.0:
    busboy: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  charenc@0.0.2:
    charenc: private
  chokidar@3.6.0:
    chokidar: private
  ci-info@4.3.0:
    ci-info: private
  client-only@0.0.1:
    client-only: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  commander@2.20.3:
    commander: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  console-table-printer@2.12.1:
    console-table-printer: private
  convert-source-map@1.9.0:
    convert-source-map: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  create-require@1.1.1:
    create-require: private
  croner@9.1.0:
    croner: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypt@0.0.2:
    crypt: private
  cssfilter@0.0.10:
    cssfilter: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  dataloader@2.2.3:
    dataloader: private
  date-fns@4.1.0:
    date-fns: private
  dateformat@4.6.3:
    dateformat: private
  debug@4.4.1(supports-color@5.5.0):
    debug: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  direction@1.0.4:
    direction: private
  doctrine@3.0.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  emoji-regex@9.2.2:
    emoji-regex: private
  end-of-stream@1.4.5:
    end-of-stream: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.25.9:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@8.57.1))(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@8.57.1))(eslint@8.57.1))(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.0.0-canary-7118f5dd7-20230705(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.1.0:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-type@19.3.0:
    file-type: private
  fill-range@7.1.1:
    fill-range: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  focus-trap@7.5.4:
    focus-trap: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.8.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.3.10:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphql-http@1.22.4(graphql@16.11.0):
    graphql-http: private
  graphql-playground-html@1.6.30:
    graphql-playground-html: private
  graphql-scalars@1.22.2(graphql@16.11.0):
    graphql-scalars: private
  graphql@16.11.0:
    graphql: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@3.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  help-me@5.0.0:
    help-me: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  http-status@2.1.0:
    http-status: private
  ieee754@1.2.1:
    ieee754: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  ignore@5.3.2:
    ignore: private
  image-size@2.0.2:
    image-size: private
  immer@9.0.21:
    immer: private
  immutable@4.3.7:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hotkey@0.2.0:
    is-hotkey: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@2.3.6:
    jackspeak: private
  jose@5.9.6:
    jose: private
  joycon@3.1.1:
    joycon: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-to-typescript@15.0.3:
    json-schema-to-typescript: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  kareem@2.6.3:
    kareem: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5@2.3.0:
    md5: private
  memoize-one@6.0.0:
    memoize-one: private
  memory-pager@1.5.0:
    memory-pager: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  monaco-editor@0.52.2:
    monaco-editor: private
  mongodb-connection-string-url@3.0.2:
    mongodb-connection-string-url: private
  mongodb@6.16.0:
    mongodb: private
  mongoose-paginate-v2@1.8.5:
    mongoose-paginate-v2: private
  mongoose@8.15.1:
    mongoose: private
  mpath@0.9.0:
    mpath: private
  mquery@5.0.0:
    mquery: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.3:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-to-formdata@4.5.1:
    object-to-formdata: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  peek-readable@5.4.2:
    peek-readable: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-pretty@13.0.0:
    pino-pretty: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.5.0:
    pino: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.4.31:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier@3.6.2:
    prettier: private
  process-warning@4.0.1:
    process-warning: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  pstree.remy@1.1.8:
    pstree.remy: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  qs-esm@7.0.2:
    qs-esm: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  react-datepicker@7.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-datepicker: private
  react-image-crop@10.1.8(react@18.3.1):
    react-image-crop: private
  react-is@16.13.1:
    react-is: private
  react-select@5.9.0(@types/react@18.3.24)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-select: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  readdirp@3.6.0:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sass@1.77.4:
    sass: private
  scheduler@0.23.2:
    scheduler: private
  scmp@2.1.0:
    scmp: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.2:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  sift@17.1.3:
    sift: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  simple-wcswidth@1.1.2:
    simple-wcswidth: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slate-history@0.86.0(slate@0.91.4):
    slate-history: private
  slate-hyperscript@0.81.3(slate@0.91.4):
    slate-hyperscript: private
  slate-react@0.92.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(slate@0.91.4):
    slate-react: private
  slate@0.91.4:
    slate: private
  sonic-boom@4.2.0:
    sonic-boom: private
  sonner@1.7.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.5.7:
    source-map: private
  sparse-bitfield@3.0.3:
    sparse-bitfield: private
  split2@4.2.0:
    split2: private
  stable-hash@0.0.5:
    stable-hash: private
  state-local@1.0.7:
    state-local: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@8.1.0:
    strtok3: private
  styled-jsx@5.1.1(react@18.3.1):
    styled-jsx: private
  stylis@4.2.0:
    stylis: private
  supports-color@5.5.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tabbable@6.2.0:
    tabbable: private
  text-table@0.2.0:
    text-table: private
  thread-stream@3.1.0:
    thread-stream: private
  tiny-invariant@1.0.6:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  token-types@6.1.1:
    token-types: private
  touch@3.1.1:
    touch: private
  tr46@5.1.1:
    tr46: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@1.4.3(typescript@5.9.2):
    ts-api-utils: private
  ts-essentials@10.0.3(typescript@5.9.2):
    ts-essentials: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsx@4.20.3:
    tsx: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uint8array-extras@1.5.0:
    uint8array-extras: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  undici@7.10.0:
    undici: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  uri-js@4.4.1:
    uri-js: private
  use-context-selector@2.0.0(react@18.3.1)(scheduler@0.25.0):
    use-context-selector: private
  use-isomorphic-layout-effect@1.2.1(@types/react@18.3.24)(react@18.3.1):
    use-isomorphic-layout-effect: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  uuid@10.0.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  whatwg-url@14.2.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  xss@1.0.15:
    xss: private
  yaml@1.10.2:
    yaml: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - unrs-resolver
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Sun, 31 Aug 2025 17:41:26 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.5.0'
  - '@emnapi/runtime@1.5.0'
  - '@emnapi/wasi-threads@1.1.0'
  - '@esbuild/aix-ppc64@0.23.1'
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.23.1'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.23.1'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.23.1'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-x64@0.23.1'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.23.1'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.23.1'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.23.1'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.23.1'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.23.1'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.23.1'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.23.1'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.23.1'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.23.1'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.23.1'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.23.1'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.23.1'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.23.1'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.23.1'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.23.1'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.23.1'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.23.1'
  - '@esbuild/win32-ia32@0.25.9'
  - '@esbuild/win32-x64@0.23.1'
  - '@esbuild/win32-x64@0.25.9'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-x64@14.2.32'
  - '@next/swc-linux-arm64-gnu@14.2.32'
  - '@next/swc-linux-arm64-musl@14.2.32'
  - '@next/swc-linux-x64-gnu@14.2.32'
  - '@next/swc-linux-x64-musl@14.2.32'
  - '@next/swc-win32-arm64-msvc@14.2.32'
  - '@next/swc-win32-ia32-msvc@14.2.32'
  - '@next/swc-win32-x64-msvc@14.2.32'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-x64-msvc@1.11.1'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
